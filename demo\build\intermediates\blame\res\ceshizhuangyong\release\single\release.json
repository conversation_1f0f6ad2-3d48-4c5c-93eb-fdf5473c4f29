[{"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_down_white_arrow_normal.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\down_white_arrow_normal.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_search_result.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_search_result.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_audio_detail.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_audio_detail.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable_main_bg.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable\\main_bg.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_live.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_live.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-nodpi_ic_base_back_pressed.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-nodpi\\ic_base_back_pressed.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_category.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_category.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable_selector_play_pause.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable\\selector_play_pause.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_ic_play_pause.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\ic_play_pause.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_info_error_img.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_info_error_img.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_demo_album.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_demo_album.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_ic_play_start.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\ic_play_start.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_emergency.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_emergency.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_kaola_login.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_kaola_login.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_ad_report.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_ad_report.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_image_bottom_gradient.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_image_bottom_gradient.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_category_info.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_category_info.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\mipmap-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable_live_listen_message_selector.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable\\live_listen_message_selector.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-xxhdpi_ic_more.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-xxhdpi\\ic_more.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_player_audio.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_player_audio.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_history.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_history.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_program_detail.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_program_detail.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable_selector_tag_frame.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable\\selector_tag_frame.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_download.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_download.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_leave_a_message.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_leave_a_message.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_collection.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_collection.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_listen_message_normal.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_listen_message_normal.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable_seekbar_progress.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable\\seekbar_progress.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-mdpi_ic_more.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-mdpi\\ic_more.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_album.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_album.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_subscribe.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_subscribe.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_album_qr_code.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_album_qr_code.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_user_duration.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_user_duration.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_column_detail.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_column_detail.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_qr_code_status_check.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_qr_code_status_check.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_record_sound_update.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_record_sound_update.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-v24_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-v24\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\mipmap-hdpi_ic_launcher_background.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\mipmap-hdpi\\ic_launcher_background.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable_base_back.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable\\base_back.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_listen_message_pressed.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_listen_message_pressed.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-xhdpi_ic_more.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-xhdpi\\ic_more.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_album.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_album.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_top_layout.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\top_layout.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-nodpi_ic_base_back.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-nodpi\\ic_base_back.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\mipmap-hdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\mipmap-hdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\mipmap-hdpi_ic_launcher_yt.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\mipmap-hdpi\\ic_launcher_yt.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_dialog_input_location.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\dialog_input_location.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_link_account.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_link_account.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_main.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_main.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_brand_info.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_brand_info.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_audios_qr_code.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_audios_qr_code.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_recommend.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_recommend.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable_live_image_top_gradient.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable\\live_image_top_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_demo_funtion.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_demo_funtion.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_screen_bullet_bg.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_screen_bullet_bg.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_interest.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_interest.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable_shape_tag_frame_normal.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable\\shape_tag_frame_normal.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\menu_menu_category_type.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\menu\\menu_category_type.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_category_member.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_category_member.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_common.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_common.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_message_send_success.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_message_send_success.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_listen_a_message.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_listen_a_message.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_subcategory.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_subcategory.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_column.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_column.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_qqmusic_login.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_qqmusic_login.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_collection_song.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_collection_song.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable_live_send_message_selector.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable\\live_send_message_selector.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_vip_qr_code.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_vip_qr_code.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_base_layout.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\base_layout.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_ic_play_pre.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\ic_play_pre.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_fragment_live.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\fragment_live.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable_down_white_arrow_selector.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable\\down_white_arrow_selector.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_interest_tag.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_interest_tag.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_send_failure_normal.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_send_failure_normal.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_vip_meals.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_vip_meals.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_login.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable_color_main_button_click_selector.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable\\color_main_button_click_selector.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_demo_pgc.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_demo_pgc.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_cancel_message_pressed.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_cancel_message_pressed.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\mipmap-hdpi_ic_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\mipmap-hdpi\\ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_qqmusic.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_qqmusic.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_category.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_category.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_user.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_user.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_keyword_search.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_keyword_search.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_ic_play_next.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\ic_play_next.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_send_message_normal.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_send_message_normal.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_cancel_message_normal.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_cancel_message_normal.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_common.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_common.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_subscribe.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_subscribe.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_send_message_pressed.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_send_message_pressed.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_history.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_history.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_close_exit.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_close_exit.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_scene.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_scene.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\anim_anim_live_record_sound.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\anim\\anim_live_record_sound.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_all_category.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_all_category.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_player.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_player.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_vip_meals.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_vip_meals.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_leave_message_speak.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_leave_message_speak.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable_live_send_failure_selector.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable\\live_send_failure_selector.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_scene_info.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_scene_info.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_ic_right_arrow.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\ic_right_arrow.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_album_detail.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_album_detail.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_message_received_bg.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_message_received_bg.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\color_selector_tab_text_color.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\color\\selector_tab_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_send_a_message.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_send_a_message.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_finish_cover.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_finish_cover.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_dialog_input_live_info.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\dialog_input_live_info.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable_live_cancel_message_selector.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable\\live_cancel_message_selector.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_collection.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_collection.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-nodpi_qr_expire_icon.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-nodpi\\qr_expire_icon.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_init.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_init.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_live_message_recording.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_live_message_recording.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_activities.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_activities.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_collection_list.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_collection_list.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_ic_more.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\ic_more.png"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_search_result.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_search_result.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_column.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_column.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_item_activity.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\item_activity.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable_shap_tag_frame_selected.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable\\shap_tag_frame_selected.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\layout_activity_search.xml.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\layout\\activity_search.xml"}, {"merged": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\res\\merged\\ceshizhuangyong\\release\\drawable-hdpi_live_cancel_a_message.png.flat", "source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\drawable-hdpi\\live_cancel_a_message.png"}]