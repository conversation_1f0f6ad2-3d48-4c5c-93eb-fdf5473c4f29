{"logs": [{"outputFile": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\incremental\\mergeCeshizhuangyongReleaseResources\\merged.dir\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\28901321bf70a14181739121c863a409\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "171,2140,2901,2911", "startColumns": "4,4,4,4", "startOffsets": "5087,135781,182814,183422", "endLines": "171,2142,2910,2999", "endColumns": "60,12,24,24", "endOffsets": "5143,135921,183417,188628"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "4,5,8,11,12,10,13,14,2,3,9,7,6,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "157,204,378,550,613,488,673,764,57,105,434,314,250,835,883,931", "endColumns": "45,44,54,61,58,60,89,67,46,50,52,62,62,46,46,46", "endOffsets": "198,244,428,607,667,544,758,827,99,151,482,372,308,877,925,973"}, "to": {"startLines": "313,314,315,316,317,318,319,320,321,322,323,324,325,370,371,372", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12288,12334,12379,12434,12496,12555,12616,12706,12774,12821,12872,12925,12988,15956,16003,16050", "endColumns": "45,44,54,61,58,60,89,67,46,50,52,62,62,46,46,46", "endOffsets": "12329,12374,12429,12491,12550,12611,12701,12769,12816,12867,12920,12983,13046,15998,16045,16092"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\values\\ids.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "44", "endOffsets": "97"}, "to": {"startLines": "541", "startColumns": "4", "startOffsets": "27159", "endColumns": "44", "endOffsets": "27199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\4a7f5220c5722fc8f5ef9e9727215758\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "172,177,178,287,288,289,290,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,460,461,465,466,467,468,469,470,471,494,495,496,497,498,499,500,501,537,538,539,540,545,549,550,551,563,566,567,568,569,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,625,629,630,631,632,633,640,648,649,653,657,661,666,672,679,683,687,692,696,700,704,708,712,716,722,726,732,736,742,746,751,755,758,762,768,772,778,782,788,791,795,799,803,807,811,812,813,814,817,820,823,826,830,831,832,833,834,837,839,841,843,848,849,853,859,863,864,866,877,878,882,888,892,893,894,898,925,929,930,934,962,1133,1159,1331,1357,1388,1396,1402,1416,1438,1443,1448,1458,1467,1476,1480,1487,1495,1502,1503,1512,1515,1518,1522,1526,1530,1533,1534,1539,1544,1554,1559,1566,1572,1573,1576,1580,1585,1587,1589,1592,1595,1597,1601,1604,1611,1614,1617,1621,1623,1627,1629,1631,1633,1637,1645,1653,1665,1671,1680,1683,1694,1697,1698,1703,1704,1709,1778,1848,1849,1859,1868,1869,1871,1875,1878,1881,1884,1887,1890,1893,1896,1900,1903,1906,1909,1913,1916,1920,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1946,1948,1949,1950,1951,1952,1953,1954,1955,1957,1958,1960,1961,1963,1965,1966,1968,1969,1970,1971,1972,1973,1975,1976,1977,1978,1979,1996,1998,2000,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2016,2017,2018,2019,2020,2021,2023,2027,2031,2032,2033,2034,2035,2036,2040,2041,2042,2043,2045,2047,2049,2051,2053,2054,2055,2056,2058,2060,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2076,2077,2078,2079,2081,2083,2084,2086,2087,2089,2091,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2106,2107,2108,2109,2111,2112,2113,2114,2115,2117,2119,2121,2123,2124,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2143,2226,2229,2232,2235,2249,2260,2315,2345,2372,2381,2456,2853,2870,3000,3147,3183,3189,3195,3218,3359,3387,3393,3397,3431,3468,3512,3578,3602,3672,3691,3717", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5148,5330,5375,10502,10543,10598,10657,10880,10961,11022,11097,11173,11250,11328,11413,11495,11571,11647,11724,11802,11908,12014,12093,12173,12230,13097,13171,13246,13311,13377,13437,13498,13570,13643,13710,13778,13837,13896,13955,14014,14073,14127,14181,14234,14288,14342,14396,14671,14745,14824,14897,14971,15042,15114,15186,15259,15316,15374,15447,15521,15595,15670,15742,15815,15885,16097,16157,16218,16287,16356,16426,16500,16576,16640,16717,16793,16870,16935,17004,17081,17156,17225,17293,17370,17436,17497,17594,17659,17728,17827,17898,17957,18015,18072,18131,18195,18266,18338,18410,18482,18554,18621,18689,18757,18816,18879,18943,19033,19124,19184,19250,19317,19383,19453,19517,19570,19637,19698,19765,19878,19936,19999,20064,20129,20204,20277,20349,20398,20459,20520,20581,20643,20707,20771,20835,20900,20963,21023,21084,21150,21209,21269,21331,21402,21462,22018,22104,22354,22444,22531,22619,22701,22784,22874,24378,24430,24488,24533,24599,24663,24720,24777,26954,27011,27059,27108,27358,27528,27575,27624,28230,28369,28433,28495,28555,28682,28756,28826,28904,28958,29028,29113,29161,29207,29268,29331,29397,29461,29532,29595,29660,29724,29785,29846,29898,29971,30045,30114,30189,30263,30337,30478,31994,32190,32268,32358,32446,32542,32951,33533,33622,33869,34150,34402,34687,35080,35557,35779,36001,36277,36504,36734,36964,37194,37424,37651,38070,38296,38721,38951,39379,39598,39881,40089,40220,40447,40873,41098,41525,41746,42171,42291,42567,42868,43192,43483,43797,43934,44065,44170,44412,44579,44783,44991,45262,45374,45486,45591,45708,45922,46068,46208,46294,46642,46730,46976,47394,47643,47725,47823,48440,48540,48792,49216,49471,49565,49654,49891,51943,52185,52287,52540,54724,65848,67364,78587,80115,81872,82498,82918,83979,85244,85500,85736,86283,86777,87382,87580,88160,88724,89099,89217,89755,89912,90108,90381,90637,90807,90948,91012,91377,91744,92420,92684,93022,93375,93469,93655,93961,94223,94348,94475,94714,94925,95044,95237,95414,95869,96050,96172,96431,96544,96731,96833,96940,97069,97344,97852,98348,99225,99519,100089,100238,100970,101142,101226,101562,101654,101932,107341,112893,112955,113585,114199,114290,114403,114632,114792,114944,115115,115281,115450,115617,115780,116023,116193,116366,116537,116811,117010,117215,117545,117629,117725,117821,117919,118019,118121,118223,118325,118427,118529,118629,118725,118837,118966,119089,119220,119351,119449,119563,119657,119797,119931,120027,120139,120239,120355,120451,120563,120663,120803,120939,121103,121233,121391,121541,121682,121826,121961,122073,122223,122351,122479,122615,122747,122877,123007,123119,124399,124545,124689,124827,124893,124983,125059,125163,125253,125355,125463,125571,125671,125751,125843,125941,126051,126129,126235,126327,126431,126541,126663,126826,126983,127063,127163,127253,127363,127453,127694,127788,127894,127986,128086,128198,128312,128428,128544,128638,128752,128864,128966,129086,129208,129290,129394,129514,129640,129738,129832,129920,130032,130148,130270,130382,130557,130673,130759,130851,130963,131087,131154,131280,131348,131476,131620,131748,131817,131912,132027,132140,132239,132348,132459,132570,132671,132776,132876,133006,133097,133220,133314,133426,133512,133616,133712,133800,133918,134022,134126,134252,134340,134448,134548,134638,134748,134832,134934,135018,135072,135136,135242,135328,135438,135522,135926,141070,141188,141303,141435,142150,142842,145988,147587,149120,149508,154243,174505,175332,188633,197201,199214,199476,199832,200662,207444,208969,209263,209486,211293,213343,215894,219745,220947,226034,227249,228658", "endLines": "172,177,178,287,288,289,290,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,460,461,465,466,467,468,469,470,471,494,495,496,497,498,499,500,501,537,538,539,540,545,549,550,551,563,566,567,568,569,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,625,629,630,631,632,633,647,648,652,656,660,665,671,678,682,686,691,695,699,703,707,711,715,721,725,731,735,741,745,750,754,757,761,767,771,777,781,787,790,794,798,802,806,810,811,812,813,816,819,822,825,829,830,831,832,833,836,838,840,842,847,848,852,858,862,863,865,876,877,881,887,891,892,893,897,924,928,929,933,961,1132,1158,1330,1356,1387,1395,1401,1415,1437,1442,1447,1457,1466,1475,1479,1486,1494,1501,1502,1511,1514,1517,1521,1525,1529,1532,1533,1538,1543,1553,1558,1565,1571,1572,1575,1579,1584,1586,1588,1591,1594,1596,1600,1603,1610,1613,1616,1620,1622,1626,1628,1630,1632,1636,1644,1652,1664,1670,1679,1682,1693,1696,1697,1702,1703,1708,1777,1847,1848,1858,1867,1868,1870,1874,1877,1880,1883,1886,1889,1892,1895,1899,1902,1905,1908,1912,1915,1919,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1945,1947,1948,1949,1950,1951,1952,1953,1954,1956,1957,1959,1960,1962,1964,1965,1967,1968,1969,1970,1971,1972,1974,1975,1976,1977,1978,1979,1997,1999,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2015,2016,2017,2018,2019,2020,2022,2026,2030,2031,2032,2033,2034,2035,2039,2040,2041,2042,2044,2046,2048,2050,2052,2053,2054,2055,2057,2059,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2075,2076,2077,2078,2080,2082,2083,2085,2086,2088,2090,2092,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2105,2106,2107,2108,2110,2111,2112,2113,2114,2116,2118,2120,2122,2123,2124,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2225,2228,2231,2234,2248,2259,2269,2344,2371,2380,2455,2852,2857,2897,3017,3182,3188,3194,3217,3358,3378,3392,3396,3402,3467,3479,3577,3601,3670,3690,3716,3725", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "5198,5370,5419,10538,10593,10652,10714,10956,11017,11092,11168,11245,11323,11408,11490,11566,11642,11719,11797,11903,12009,12088,12168,12225,12283,13166,13241,13306,13372,13432,13493,13565,13638,13705,13773,13832,13891,13950,14009,14068,14122,14176,14229,14283,14337,14391,14445,14740,14819,14892,14966,15037,15109,15181,15254,15311,15369,15442,15516,15590,15665,15737,15810,15880,15951,16152,16213,16282,16351,16421,16495,16571,16635,16712,16788,16865,16930,16999,17076,17151,17220,17288,17365,17431,17492,17589,17654,17723,17822,17893,17952,18010,18067,18126,18190,18261,18333,18405,18477,18549,18616,18684,18752,18811,18874,18938,19028,19119,19179,19245,19312,19378,19448,19512,19565,19632,19693,19760,19873,19931,19994,20059,20124,20199,20272,20344,20393,20454,20515,20576,20638,20702,20766,20830,20895,20958,21018,21079,21145,21204,21264,21326,21397,21457,21525,22099,22186,22439,22526,22614,22696,22779,22869,22960,24425,24483,24528,24594,24658,24715,24772,24826,27006,27054,27103,27154,27387,27570,27619,27665,28257,28428,28490,28550,28607,28751,28821,28899,28953,29023,29108,29156,29202,29263,29326,29392,29456,29527,29590,29655,29719,29780,29841,29893,29966,30040,30109,30184,30258,30332,30473,30543,32042,32263,32353,32441,32537,32627,33528,33617,33864,34145,34397,34682,35075,35552,35774,35996,36272,36499,36729,36959,37189,37419,37646,38065,38291,38716,38946,39374,39593,39876,40084,40215,40442,40868,41093,41520,41741,42166,42286,42562,42863,43187,43478,43792,43929,44060,44165,44407,44574,44778,44986,45257,45369,45481,45586,45703,45917,46063,46203,46289,46637,46725,46971,47389,47638,47720,47818,48435,48535,48787,49211,49466,49560,49649,49886,51938,52180,52282,52535,54719,65843,67359,78582,80110,81867,82493,82913,83974,85239,85495,85731,86278,86772,87377,87575,88155,88719,89094,89212,89750,89907,90103,90376,90632,90802,90943,91007,91372,91739,92415,92679,93017,93370,93464,93650,93956,94218,94343,94470,94709,94920,95039,95232,95409,95864,96045,96167,96426,96539,96726,96828,96935,97064,97339,97847,98343,99220,99514,100084,100233,100965,101137,101221,101557,101649,101927,107336,112888,112950,113580,114194,114285,114398,114627,114787,114939,115110,115276,115445,115612,115775,116018,116188,116361,116532,116806,117005,117210,117540,117624,117720,117816,117914,118014,118116,118218,118320,118422,118524,118624,118720,118832,118961,119084,119215,119346,119444,119558,119652,119792,119926,120022,120134,120234,120350,120446,120558,120658,120798,120934,121098,121228,121386,121536,121677,121821,121956,122068,122218,122346,122474,122610,122742,122872,123002,123114,123254,124540,124684,124822,124888,124978,125054,125158,125248,125350,125458,125566,125666,125746,125838,125936,126046,126124,126230,126322,126426,126536,126658,126821,126978,127058,127158,127248,127358,127448,127689,127783,127889,127981,128081,128193,128307,128423,128539,128633,128747,128859,128961,129081,129203,129285,129389,129509,129635,129733,129827,129915,130027,130143,130265,130377,130552,130668,130754,130846,130958,131082,131149,131275,131343,131471,131615,131743,131812,131907,132022,132135,132234,132343,132454,132565,132666,132771,132871,133001,133092,133215,133309,133421,133507,133611,133707,133795,133913,134017,134121,134247,134335,134443,134543,134633,134743,134827,134929,135013,135067,135131,135237,135323,135433,135517,135637,141065,141183,141298,141430,142145,142837,143354,147582,149115,149503,154238,174500,174760,176837,189661,199209,199471,199827,200657,207439,208573,209258,209481,209808,213338,213986,219740,220942,225021,227244,228653,229127"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\88afcf77c9f945c4551c128d01d9880b\\fragment-1.2.5\\res\\values\\values.xml", "from": {"startLines": "2,3,4,9", "startColumns": "4,4,4,4", "startOffsets": "55,112,176,346", "endLines": "2,3,8,12", "endColumns": "56,63,24,24", "endOffsets": "107,171,341,490"}, "to": {"startLines": "543,565,3093,3098", "startColumns": "4,4,4,4", "startOffsets": "27243,28305,194637,194807", "endLines": "543,565,3097,3101", "endColumns": "56,63,24,24", "endOffsets": "27295,28364,194802,194951"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\values\\arrays.xml", "from": {"startLines": "61,81,13,43,144,139,2,38,85,25,71,153,149,157,163,89,56,121,130,168,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1763,2288,370,1263,3802,3676,57,1145,2378,763,2004,4035,3919,4188,4358,2465,1643,3228,3450,4500,1889", "endLines": "64,83,23,54,147,142,12,41,87,36,79,156,152,162,167,118,59,128,137,173,69", "endColumns": "19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,19,19,19,19,19", "endOffsets": "1881,2370,755,1635,3911,3794,364,1255,2457,1137,2280,4182,4029,4352,4494,3218,1755,3442,3668,4662,1996"}, "to": {"startLines": "2,6,9,20,32,36,40,51,55,58,70,79,83,87,93,98,128,132,140,148,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,310,690,1056,1167,1287,1589,1701,1783,2151,2424,2573,2685,2849,2986,3715,3829,4041,4257,4419", "endLines": "5,8,19,31,35,39,50,54,57,69,78,82,86,92,97,127,131,139,147,153,157", "endColumns": "19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,19,19,19,19,19", "endOffsets": "220,305,685,1051,1162,1282,1584,1696,1778,2146,2419,2568,2680,2844,2981,3710,3824,4036,4252,4414,4523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\43e13e1514558d13002012daba640166\\constraintlayout-1.1.3\\res\\values\\values.xml", "from": {"startLines": "2,3,11,12,13,14,15,19,20,21,22,25,26,29,32,33,34,35,36,39,42,43,44,45,50,53,56,57,58,63,64,65,68,71,72,75,78,81,84,85,88,91,92,97,98,103,106,109,110,111,112,113,114,115,116,117,118,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,116,407,455,507,568,614,741,802,862,932,1065,1133,1262,1388,1450,1515,1583,1650,1773,1898,1965,2030,2095,2276,2397,2518,2584,2651,2861,2930,2996,3121,3247,3314,3440,3567,3692,3819,3884,4010,4133,4198,4406,4473,4653,4773,4893,4958,5020,5082,5144,5203,5263,5324,5385,5444,5819,8395,8527,11791", "endLines": "2,10,11,12,13,14,18,19,20,21,24,25,28,31,32,33,34,35,38,41,42,43,44,49,52,55,56,57,62,63,64,67,70,71,74,77,80,83,84,87,90,91,96,97,102,105,108,109,110,111,112,113,114,115,116,117,126,127,128,129,130", "endColumns": "60,11,47,51,60,45,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11,2575,131,3263,107", "endOffsets": "111,402,450,502,563,609,736,797,857,927,1060,1128,1257,1383,1445,1510,1578,1645,1768,1893,1960,2025,2090,2271,2392,2513,2579,2646,2856,2925,2991,3116,3242,3309,3435,3562,3687,3814,3879,4005,4128,4193,4401,4468,4648,4768,4888,4953,5015,5077,5139,5198,5258,5319,5380,5439,5814,8390,8522,11786,11894"}, "to": {"startLines": "158,159,167,168,169,170,173,179,180,181,182,185,186,189,192,193,194,195,196,199,202,203,204,205,210,213,216,217,218,223,224,225,228,231,232,235,238,241,244,245,248,251,252,257,258,263,266,269,270,271,272,273,274,275,276,277,278,2898,2899,2900,3146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4528,4589,4880,4928,4980,5041,5203,5424,5485,5545,5615,5748,5816,5945,6071,6133,6198,6266,6333,6456,6581,6648,6713,6778,6959,7080,7201,7267,7334,7544,7613,7679,7804,7930,7997,8123,8250,8375,8502,8567,8693,8816,8881,9089,9156,9336,9456,9576,9641,9703,9765,9827,9886,9946,10007,10068,10127,176842,179418,179550,197093", "endLines": "158,166,167,168,169,170,176,179,180,181,184,185,188,191,192,193,194,195,198,201,202,203,204,209,212,215,216,217,222,223,224,227,230,231,234,237,240,243,244,247,250,251,256,257,262,265,268,269,270,271,272,273,274,275,276,277,286,2898,2899,2900,3146", "endColumns": "60,11,47,51,60,45,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11,2575,131,3263,107", "endOffsets": "4584,4875,4923,4975,5036,5082,5325,5480,5540,5610,5743,5811,5940,6066,6128,6193,6261,6328,6451,6576,6643,6708,6773,6954,7075,7196,7262,7329,7539,7608,7674,7799,7925,7992,8118,8245,8370,8497,8562,8688,8811,8876,9084,9151,9331,9451,9571,9636,9698,9760,9822,9881,9941,10002,10063,10122,10497,179413,179545,182809,197196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\61680fb4a271c1d4e88bc37adaba525d\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "462,463,464,472,473,474,546,3403", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "22191,22250,22298,22965,23040,23116,27392,209813", "endLines": "462,463,464,472,473,474,546,3430", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "22245,22293,22349,23035,23111,23183,27453,211288"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2,17,16,15,5,12,29,22,9,10,8,14,18,11,13,24,25,27,6,28,19,26,7,23,20,21,30,1", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "54,797,742,688,121,505,1425,1052,338,387,268,620,849,445,559,1159,1214,1334,164,1382,901,1277,212,1107,951,1004,1483,17", "endColumns": "38,50,53,52,41,52,56,53,47,56,68,66,50,58,59,53,61,46,46,41,48,55,54,50,51,46,35,35", "endOffsets": "88,843,791,736,158,553,1477,1101,381,439,332,682,895,499,614,1208,1271,1376,206,1419,945,1328,262,1153,998,1046,1514,48"}, "to": {"startLines": "599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,626,628", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "30618,30657,30708,30762,30815,30857,30910,30967,31021,31069,31126,31195,31262,31313,31372,31432,31486,31548,31595,31642,31684,31733,31789,31844,31895,31947,32047,32154", "endColumns": "38,50,53,52,41,52,56,53,47,56,68,66,50,58,59,53,61,46,46,41,48,55,54,50,51,46,35,35", "endOffsets": "30652,30703,30757,30810,30852,30905,30962,31016,31064,31121,31190,31257,31308,31367,31427,31481,31543,31590,31637,31679,31728,31784,31839,31890,31942,31989,32078,32185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\5d415039a9196d7dcbbfb2b7a48427a4\\core-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,83,84,88,89,90,91,98,141,173,210", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,319,389,457,529,599,660,734,807,868,929,991,1055,1117,1178,1246,1346,1406,1472,1545,1614,1671,1723,1785,1857,1933,1998,2057,2116,2176,2236,2296,2356,2416,2476,2536,2596,2656,2716,2775,2835,2895,2955,3015,3075,3135,3195,3255,3315,3375,3434,3494,3554,3613,3672,3731,3790,3849,3908,3943,3978,4033,4096,4151,4209,4266,4316,4377,4434,4468,4503,4538,4608,4679,4796,4997,5107,5308,5437,5509,5576,5874,8780,10845,12605", "endLines": "2,3,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,82,83,87,88,89,90,97,140,172,209,216", "endColumns": "68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,56,49,60,56,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24", "endOffsets": "119,182,384,452,524,594,655,729,802,863,924,986,1050,1112,1173,1241,1341,1401,1467,1540,1609,1666,1718,1780,1852,1928,1993,2052,2111,2171,2231,2291,2351,2411,2471,2531,2591,2651,2711,2770,2830,2890,2950,3010,3070,3130,3190,3250,3310,3370,3429,3489,3549,3608,3667,3726,3785,3844,3903,3938,3973,4028,4091,4146,4204,4261,4311,4372,4429,4463,4498,4533,4603,4674,4791,4992,5102,5303,5432,5504,5571,5869,8775,10840,12600,12977"}, "to": {"startLines": "349,350,453,454,455,456,457,458,459,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,547,548,552,553,554,555,556,557,558,559,560,561,562,570,627,1980,1981,1986,1989,1994,2138,2139,2863,3018,3061,3102,3139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14450,14519,21530,21600,21668,21740,21810,21871,21945,23188,23249,23310,23372,23436,23498,23559,23627,23727,23787,23853,23926,23995,24052,24104,24831,24903,24979,25044,25103,25162,25222,25282,25342,25402,25462,25522,25582,25642,25702,25762,25821,25881,25941,26001,26061,26121,26181,26241,26301,26361,26421,26480,26540,26600,26659,26718,26777,26836,26895,27458,27493,27670,27725,27788,27843,27901,27958,28008,28069,28126,28160,28195,28612,32083,123259,123376,123643,123936,124203,135642,135714,175034,189666,192572,194956,196716", "endLines": "349,350,453,454,455,456,457,458,459,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,547,548,552,553,554,555,556,557,558,559,560,561,562,570,627,1980,1984,1986,1992,1994,2138,2139,2869,3060,3092,3138,3145", "endColumns": "68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,56,49,60,56,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24", "endOffsets": "14514,14577,21595,21663,21735,21805,21866,21940,22013,23244,23305,23367,23431,23493,23554,23622,23722,23782,23848,23921,23990,24047,24099,24161,24898,24974,25039,25098,25157,25217,25277,25337,25397,25457,25517,25577,25637,25697,25757,25816,25876,25936,25996,26056,26116,26176,26236,26296,26356,26416,26475,26535,26595,26654,26713,26772,26831,26890,26949,27488,27523,27720,27783,27838,27896,27953,28003,28064,28121,28155,28190,28225,28677,32149,123371,123572,123748,124132,124327,135709,135776,175327,192567,194632,196711,197088"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\generated\\res\\resValues\\ceshizhuangyong\\release\\values\\gradleResValues.xml", "from": {"startLines": "6", "startColumns": "4", "startOffsets": "176", "endColumns": "69", "endOffsets": "241"}, "to": {"startLines": "598", "startColumns": "4", "startOffsets": "30548", "endColumns": "69", "endOffsets": "30613"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\c03fe72d5231576c504b787f06aff13d\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "351,490,491,492,493,1985,1987,1988,1993,1995", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "14582,24166,24219,24272,24325,123577,123753,123875,124137,124332", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "14666,24214,24267,24320,24373,123638,123870,123931,124198,124394"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\b0d45b121b2647db08cdfb2494909ba6\\jetified-tkrefreshlayout-1.0.7\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,7,6", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,94,134,175,1224,216", "endColumns": "38,39,40,40,38,1007", "endOffsets": "89,129,170,211,1258,1219"}, "to": {"startLines": "291,292,293,294,542,3671", "startColumns": "4,4,4,4,4,4", "startOffsets": "10719,10758,10798,10839,27204,225026", "endColumns": "38,39,40,40,38,1007", "endOffsets": "10753,10793,10834,10875,27238,226029"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\sdk\\build\\intermediates\\packaged_res\\release\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "45", "endOffsets": "96"}, "to": {"startLines": "326", "startColumns": "4", "startOffsets": "13051", "endColumns": "45", "endOffsets": "13092"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\9d215733c6223810f748caa85622b789\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2270,2297,2304,3480,3507", "startColumns": "4,4,4,4,4", "startOffsets": "143359,145006,145391,213991,215624", "endLines": "2296,2303,2314,3506,3511", "endColumns": "24,24,24,24,24", "endOffsets": "145001,145386,145983,215619,215889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\cf411204873ab8bb1dbda4fad57b468b\\lifecycle-runtime-2.5.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "564", "startColumns": "4", "startOffsets": "28262", "endColumns": "42", "endOffsets": "28300"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\values\\attrs.xml", "from": {"startLines": "2,8", "startColumns": "4,4", "startOffsets": "57,351", "endLines": "6,15", "endColumns": "24,24", "endOffsets": "343,750"}, "to": {"startLines": "2858,3379", "startColumns": "4,4", "startOffsets": "174765,208578", "endLines": "2862,3386", "endColumns": "24,24", "endOffsets": "175029,208964"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\88a1d4513295438135310afef65e118f\\jetified-glide-4.11.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "544", "startColumns": "4", "startOffsets": "27300", "endColumns": "57", "endOffsets": "27353"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "57", "endLines": "8", "endColumns": "12", "endOffsets": "376"}, "to": {"startLines": "634", "startColumns": "4", "startOffsets": "32632", "endLines": "639", "endColumns": "12", "endOffsets": "32946"}}]}]}