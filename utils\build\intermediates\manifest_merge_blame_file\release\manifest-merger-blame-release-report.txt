1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.kaolafm.base.utils"
4    android:versionCode="308"
5    android:versionName="3.0.11.pre11.wuautoplay_token3" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
8-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\src\main\AndroidManifest.xml
9        android:targetSdkVersion="28" />
9-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
11-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\src\main\AndroidManifest.xml:4:5-79
11-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\src\main\AndroidManifest.xml:4:22-76
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\src\main\AndroidManifest.xml:5:5-80
12-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\src\main\AndroidManifest.xml:5:22-77
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
13-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\src\main\AndroidManifest.xml:6:5-81
13-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\src\main\AndroidManifest.xml:6:22-78
14
15</manifest>
