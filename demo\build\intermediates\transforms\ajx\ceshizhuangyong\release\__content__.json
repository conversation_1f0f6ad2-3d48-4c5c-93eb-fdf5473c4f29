[{"name": "org.aspectj:aspectjrt:1.9.5", "index": 0, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.github.tbruyelle:rxpermissions:v0.11", "index": 1, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.trello.rxlifecycle3:rxlifecycle-components:3.1.0", "index": 2, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.appcompat:appcompat:1.1.0", "index": 3, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.constraintlayout:constraintlayout:1.1.3", "index": 4, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.lcodecorex:tkrefreshlayout:1.0.7", "index": 5, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.recyclerview:recyclerview:1.0.0", "index": 6, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.github.bumptech.glide:glide:4.11.0", "index": 7, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.jakewharton:butterknife:10.2.1", "index": 8, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.netease.nimlib:basesdk:5.1.1", "index": 9, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.netease.nimlib:chatroom:5.1.1", "index": 10, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.elvishew:xlog:1.6.1", "index": 11, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.orhanobut:logger:2.2.0", "index": 12, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.trello.rxlifecycle3:rxlifecycle-android:3.1.0", "index": 13, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.trello.rxlifecycle3:rxlifecycle:3.1.0", "index": 14, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.multidex:multidex:2.0.1", "index": 15, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.appcompat:appcompat-resources:1.1.0", "index": 16, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.legacy:legacy-support-v4:1.0.0", "index": 17, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.legacy:legacy-support-core-ui:1.0.0", "index": 18, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.drawerlayout:drawerlayout:1.0.0", "index": 19, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.jakewharton:butterknife-runtime:10.2.1", "index": 20, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "android.local.jars:jetified-kl_sdk_vehicle_DEV_V2.3.0.jar:3a2c8abe6f27cf73e9d75d6855d88f6301baf3ef", "index": 21, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.lifecycle:lifecycle-extensions:2.2.0", "index": 22, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.fragment:fragment:1.2.5", "index": 23, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.vectordrawable:vectordrawable-animated:1.1.0", "index": 24, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.vectordrawable:vectordrawable:1.1.0", "index": 25, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.viewpager:viewpager:1.0.0", "index": 26, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.coordinatorlayout:coordinatorlayout:1.0.0", "index": 27, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.slidingpanelayout:slidingpanelayout:1.0.0", "index": 28, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.customview:customview:1.0.0", "index": 29, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.legacy:legacy-support-core-utils:1.0.0", "index": 30, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.swiperefreshlayout:swiperefreshlayout:1.0.0", "index": 31, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.asynclayoutinflater:asynclayoutinflater:1.0.0", "index": 32, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.media:media:1.0.0", "index": 33, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.loader:loader:1.0.0", "index": 34, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.activity:activity:1.1.0", "index": 35, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.core:core:1.1.0", "index": 36, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.cursoradapter:cursoradapter:1.0.0", "index": 37, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.github.bumptech.glide:gifdecoder:4.11.0", "index": 38, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.exifinterface:exifinterface:1.0.0", "index": 39, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.lifecycle:lifecycle-process:2.2.0", "index": 40, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.lifecycle:lifecycle-service:2.2.0", "index": 41, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.lifecycle:lifecycle-runtime:2.5.0", "index": 42, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.versionedparcelable:versionedparcelable:1.1.0", "index": 43, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.interpolator:interpolator:1.0.0", "index": 44, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.jakewharton:butterknife-annotations:10.2.1", "index": 45, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.collection:collection:1.1.0", "index": 46, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.lifecycle:lifecycle-viewmodel-savedstate:2.2.0", "index": 47, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.lifecycle:lifecycle-viewmodel:2.2.0", "index": 48, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.lifecycle:lifecycle-livedata:2.2.0", "index": 49, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.lifecycle:lifecycle-livedata-core:2.2.0", "index": 50, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.room:room-rxjava2:2.4.1", "index": 51, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.room:room-runtime:2.4.1", "index": 52, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.arch.core:core-runtime:2.1.0", "index": 53, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.savedstate:savedstate:1.0.0", "index": 54, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.arch.core:core-common:2.1.0", "index": 55, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.lifecycle:lifecycle-common:2.5.0", "index": 56, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.room:room-common:2.4.1", "index": 57, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.sqlite:sqlite-framework:2.2.0", "index": 58, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.sqlite:sqlite:2.2.0", "index": 59, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.documentfile:documentfile:1.0.0", "index": 60, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.localbroadcastmanager:localbroadcastmanager:1.0.0", "index": 61, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.print:print:1.0.0", "index": 62, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.annotation:annotation:1.3.0", "index": 63, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.constraintlayout:constraintlayout-solver:1.1.3", "index": 64, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.github.bumptech.glide:disklrucache:4.11.0", "index": 65, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.github.bumptech.glide:annotations:4.11.0", "index": 66, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "io.reactivex.rxjava2:rxandroid:2.1.1", "index": 67, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "io.reactivex.rxjava2:rxjava:2.2.19", "index": 68, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.squareup.retrofit2:adapter-rxjava2:2.9.0", "index": 69, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "org.reactivestreams:reactive-streams:1.0.3", "index": 70, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "androidx.annotation:annotation-experimental:1.1.0", "index": 71, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.squareup.retrofit2:retrofit:2.9.0", "index": 72, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.squareup.retrofit2:converter-gson:2.9.0", "index": 73, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "io.socket:socket.io-client:1.0.1", "index": 74, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "io.socket:engine.io-client:1.0.1", "index": 75, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.squareup.okhttp3:okhttp:3.12.12", "index": 76, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "org.greenrobot:greendao:3.3.0", "index": 77, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.google.code.gson:gson:2.10.1", "index": 78, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.google.dagger:dagger:2.25.4", "index": 79, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.alibaba:<PERSON><PERSON><PERSON>:1.2.83", "index": 80, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "com.squareup.okio:okio:1.15.0", "index": 81, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "org.greenrobot:greendao-api:3.3.0", "index": 82, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "javax.inject:javax.inject:1", "index": 83, "scopes": ["EXTERNAL_LIBRARIES"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": ":sdk", "index": 84, "scopes": ["SUB_PROJECTS"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": ":socket", "index": 85, "scopes": ["SUB_PROJECTS"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": ":player", "index": 86, "scopes": ["SUB_PROJECTS"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": ":api", "index": 87, "scopes": ["SUB_PROJECTS"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": ":ad", "index": 88, "scopes": ["SUB_PROJECTS"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": ":core", "index": 89, "scopes": ["SUB_PROJECTS"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": ":utils", "index": 90, "scopes": ["SUB_PROJECTS"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": ":adreport", "index": 91, "scopes": ["SUB_PROJECTS"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": ":report", "index": 92, "scopes": ["SUB_PROJECTS"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "633271681d5f056922cd73e4005d3ed966829103", "index": 93, "scopes": ["PROJECT"], "types": ["CLASSES"], "format": "JAR", "present": true}, {"name": "exclude", "index": 94, "scopes": ["PROJECT"], "types": ["CLASSES"], "format": "JAR", "present": true}]