{"logs": [{"outputFile": "C:\\Users\\<USER>\\AndroidStudioProjects\\sdk\\devlop\\kaolaopensdk\\demo\\build\\intermediates\\incremental\\mergeCeshizhuangyongReleaseResources\\merged.dir\\values-en-rIN\\values-en-rIN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\5d415039a9196d7dcbbfb2b7a48427a4\\core-1.1.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2844", "endColumns": "100", "endOffsets": "2940"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\4a7f5220c5722fc8f5ef9e9727215758\\appcompat-1.1.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,870,961,1053,1148,1242,1343,1436,1531,1625,1716,1807,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,75,90,91,94,93,100,92,94,93,90,90,80,102,102,98,104,103,103,155,99,81", "endOffsets": "204,304,412,496,596,711,789,865,956,1048,1143,1237,1338,1431,1526,1620,1711,1802,1883,1986,2089,2188,2293,2397,2501,2657,2757,2839"}}]}]}