package com.kaolafm.opensdk.player.logic.playcontrol;

import android.util.Log;

import com.kaolafm.base.utils.DateUtil;
import java.util.List;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.media.model.AudioFileInfo;
import com.kaolafm.opensdk.api.media.model.ToneQuality;
import com.kaolafm.opensdk.player.core.ijk.VideoView;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.PlayItemConstants;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.TimeInfoData;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.playlist.BroadcastPlayListControl;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import io.reactivex.Flowable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;

/**
 * 广播播放控制 do
 * <AUTHOR> shi qian
 */
public class BroadcastPlayControl extends BasePlayControl {
    private final String TAG = "BroadcastPlayControl";
    /**
     * 等待获取下一首广播时间
     */
    //private static final int WAIT_TIMER = 10000;
    private long progressTime;
    private long startTime;
    private long endTime;
    private long curServiceTime;
    private long livingTotalTime;
    private Disposable mDisposable;
    private IPlayerStateListener mIPlayerStateListener;

    //是否正在播放直播节目
    private boolean isPlayLiving = false;

    @Override
    public void setPlayStateListener(BasePlayStateListener iPlayerStateListener) {
        super.setPlayStateListener(iPlayerStateListener);
        mIPlayerStateListener = iPlayerStateListener;
    }

    public BroadcastPlayControl() {
        super();
    }

    @Override
    public void notifyPlayItemChangToOtherType() {
        super.notifyPlayItemChangToOtherType();
        Log.i(TAG, "notifyPlayItemChangToOtherType");
        mPlayItem = null;
        isPlayLiving = false;
        stopTimer();
    }

    @Override
    public void preStart(PlayItem playItem) {
        super.preStart(playItem);
        Log.i(TAG, "preStart");
        if (mPlayItem != null && playItem != null && !mPlayItem.getRadioId().equals(playItem.getRadioId())) {
            //如果调用start播放时与之前正播的广播不是同一个电台
            //用于解决播放检修台A的下一个电台B时手动切换到A由于检修又自动播放B时音频不能播放的问题
            mPlayItem = null;
            isPlayLiving = false;
            stopTimer();
        }
    }

    @Override
    public void start(int type, PlayItem playItem, VideoView videoView, boolean isPlayNow) {
        PlayerLogUtil.log(TAG, "start" + " type = " + type + " playItem = " + playItem + " isPlayNow = " + isPlayNow);
        boolean isIgnorePlay = mPlayItem instanceof BroadcastPlayItem    //正在播放的是广播节目
                && playItem instanceof BroadcastPlayItem    //想要播放的是广播节目
                && mPlayItem.getRadioId().equals(playItem.getRadioId())   //正在播放的是否是同一个广播节目
                && isPlayLiving //是否正在播放直播节目
                && playItem.isLiving()  //要播放的节目是不是直播
                ;
        PlayerLogUtil.log(TAG, "start", "验证同一广播直播：oldRadioId = " + (mPlayItem != null ? mPlayItem.getRadioId() : "") + ", isPlayLiving = " + isPlayLiving + ", newRadioId = " + (playItem != null ? playItem.getRadioId() : "") + ", newLiving = " + (playItem != null ? String.valueOf(playItem.isLiving()) : "") + ", isIgnorePlay = " + isIgnorePlay);
        if (isIgnorePlay) {
            //上面的验证通过，下面就应该验证播放地址是否相同，否则重新拉流
            String playUrl = removeAntiLeech(getPlayUrl(mPlayItem, ((BroadcastPlayItem) mPlayItem).getPlayInfoList()));
            String newPlayUrl = removeAntiLeech(getPlayUrl(playItem, ((BroadcastPlayItem) playItem).getPlayInfoList()));
            isIgnorePlay = playUrl != null && playUrl.equals(newPlayUrl);
            PlayerLogUtil.log(TAG, "start", "验证新地址：playUrl = " + playUrl + ", newPlayUrl = " + newPlayUrl + ", isIgnorePlay = " + isIgnorePlay);
        }
        mPlayItem = playItem;
        if (playItem.getType() != PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            stopTimer();
            return;
        }
        BroadcastPlayItem playItemTemp = (BroadcastPlayItem) playItem;
        if (playItemTemp.getStatus() == PlayerConstants.BROADCAST_STATUS_LIVING) {
            isPlayLiving = true;
            TimeInfoData timeInfoData = playItemTemp.getTimeInfoData();
            curServiceTime = timeInfoData.getCurSystemTime();
            startTime = timeInfoData.getStartTime();
            endTime = timeInfoData.getFinishTime();
            livingTotalTime = endTime - startTime;
            progressTime = curServiceTime - startTime;
            if (progressTime < 0) {
                progressTime = 0;
            }
            PlayerLogUtil.log(TAG, "start", "live count time: curServiceTime" + curServiceTime + ", startTime = " + timeInfoData.getStartTime() + ", endTime = " + timeInfoData.getEndTime());
            startTimer();
        } else {
            isPlayLiving = false;
            stopTimer();
        }
        boolean broadcastAutoPlayToNextItem = isBroadcastAutoPlayToNextItem();
        if (isInterceptBroadcastAutoPlay()) { //TODO:Wangyi 这个要细查一下。
            PlayerLogUtil.log(TAG, "start", "intercept normal play");
            setPlayNowOnPrepare(isPlayNow);
            setPlayUrl(mPlayItem.getPlayUrl(), 0, 0, getVideoView());
            mIPlayerStateListener.onPlayerPaused(mPlayItem);
        } else {
            if (broadcastAutoPlayToNextItem && isIgnorePlay) {
                PlayerLogUtil.log(TAG, "start", "broadcast-living auto play next and ignore restart");
                //TODO:Wangyi 这里可能有点问题
                mIPlayerStateListener.onPlayerPlaying(playItem);
                return;
            }
            if (playItemTemp.getTimeInfoData().getStartTime() > DateUtil.getServerTime()) {
                mIPlayerStateListener.onPlayerFailed(playItem, 404, 404);
                return;
            }
            super.start(type, playItem,videoView, isPlayNow);
        }
    }

    @Override
    ToneQuality getToneQuality(PlayItem playItem, List<AudioFileInfo> playListUrlInfos) {
        return null;
    }

    @Override
    void requestPlayUrl(PlayItem playItem, OnGetPlayUrlData callback) {
        //设置并回调播放地址
        BroadcastPlayItem broadcastPlayItem = (BroadcastPlayItem) playItem;
        // 打印节目状态日志
        String statusText = getStatusText(broadcastPlayItem.getStatus());
        PlayerLogUtil.log(TAG, "requestPlayUrl", "播放节目状态: " + statusText + ", audioId=" + playItem.getAudioId() + ", title=" + playItem.getTitle());
        if (broadcastPlayItem.isLiving()) {
            setPlayUrl(playItem, broadcastPlayItem.getPlayInfoList());
            callback.onDataGet(playItem.getPlayUrl());
        } else {
            // 回听状态，检查是否有有效的回听地址
            if (broadcastPlayItem.getBackPlayInfoList() == null || broadcastPlayItem.getBackPlayInfoList().isEmpty()) {
                PlayerLogUtil.log(TAG, "requestPlayUrl", "需要从服务端获取回听地址. audioId=" + playItem.getAudioId());
                // 没有回听地址信息，需要从服务器获取
                requestPlaybackUrlFromServer(playItem, callback);
            } else {
                setPlayUrl(playItem, broadcastPlayItem.getBackPlayInfoList());
                callback.onDataGet(playItem.getPlayUrl());
            }
        }
    }

    @Override
    String getPlayItemResourceType(PlayItem playItem) {
        return "m3u8";
    }

    /**
     * 获取状态文本描述
     */
    private String getStatusText(int status) {
        switch (status) {
            case PlayerConstants.BROADCAST_STATUS_LIVING:
                return "直播中";
            case PlayerConstants.BROADCAST_STATUS_PLAYBACK:
                return "回听";
            case PlayerConstants.BROADCAST_STATUS_DEFAULT:
                return "默认状态";
            default:
                return "未知状态(" + status + ")";
        }
    }

    /**
     * 从服务器获取回听地址
     */
    private void requestPlaybackUrlFromServer(PlayItem playItem, OnGetPlayUrlData callback) {
        PlayerLogUtil.log(TAG, "requestPlaybackUrlFromServer", "从服务端获取回听地址. audioId=" + playItem.getAudioId());

        // 通过PlayerManager获取播放列表控制器
        PlayerManager playerManager = PlayerManager.getInstance();
        if (playerManager != null && playerManager.getPlayListControl() instanceof BroadcastPlayListControl) {
            BroadcastPlayListControl playListControl = (BroadcastPlayListControl) playerManager.getPlayListControl();

            // 使用播放列表控制器的initLiving方法来获取最新的播放地址
            // 这里我们需要创建一个临时的监听器来处理结果
            playListControl.refreshPlayItemUrl(playItem, new IPlayListGetListener() {
                @Override
                public void onDataGet(PlayItem updatedPlayItem, List<PlayItem> playItemArrayList) {
                    String newUrl = updatedPlayItem.getPlayUrl();
                    PlayerLogUtil.log(TAG, "requestPlaybackUrlFromServer", "success, new url=" + newUrl);
                    // 验证回听地址是否有效
                    if (StringUtil.isEmpty(newUrl) || updatedPlayItem.getStatus() != PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
                        // 返回的是直播地址或空地址，说明回听还没生成好
                        PlayerLogUtil.log(TAG, "requestPlaybackUrlFromServer", "回听正在生成中，请稍后再试. status=" + updatedPlayItem.getStatus());
                        callback.onDataGet(null);
                    } else {
                        PlayerLogUtil.log(TAG, "requestPlaybackUrlFromServer", "获取到有效回听地址: " + newUrl);
                        callback.onDataGet(newUrl);
                    }
                }

                @Override
                public void onDataGetError(PlayItem playItem, int errorCode, int errorExtra) {
                    PlayerLogUtil.log(TAG, "requestPlaybackUrlFromServer", "error, code=" + errorCode);
                    // 获取失败时，返回空地址
                    callback.onDataGet(null);
                }
            });
        } else {
            PlayerLogUtil.log(TAG, "requestPlaybackUrlFromServer", "cannot get BroadcastPlayListControl");
            callback.onDataGet(null);
        }
    }

    /**
     * 移除防盗链
     *
     * @param urlStr
     * @return
     */
    private String removeAntiLeech(String urlStr) {
        if (StringUtil.isEmpty(urlStr)) return "";
        StringBuilder path = new StringBuilder(urlStr);
        if (urlStr.contains("?")) {
            int index = urlStr.indexOf("?");
            path = new StringBuilder(urlStr.substring(0, index));
            if (urlStr.length() > index + 1)
                urlStr = urlStr.substring(index + 1);
            else urlStr = "";
        }
        if (StringUtil.isEmpty(urlStr)) return path.toString();
        String[] params = urlStr.split("&");
        Map<String, Object> mapParam = new HashMap<String, Object>();
        for (String s : params) {
            int index = s.indexOf("=");
            if (index > 0) {
                mapParam.put(s.substring(0, index), s.length() > index + 1 ? s.substring(index + 1) : "");
            }
        }
        for (Map.Entry<String, Object> entry : mapParam.entrySet()) {
            if ("type".equals(entry.getKey())) {
                path.append("?").append("type=").append(entry.getValue());
                break;
            }
        }
        return path.toString();
    }

    private boolean isInterceptBroadcastAutoPlay() {
        if (mPlayItem.getType() != PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            return false;
        }
        String type = mPlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
        if (!StringUtil.isEmpty(type)) {
            mPlayItem.removeMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
            return true;
        }
        return false;
    }

    private boolean isBroadcastAutoPlayToNextItem() {
        if (mPlayItem.getType() != PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            return false;
        }
        String type = mPlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_AUTO_PLAY_NEXT_ITEM);
        if (!StringUtil.isEmpty(type)) {
            mPlayItem.removeMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_AUTO_PLAY_NEXT_ITEM);
            return true;
        }
        return false;
    }

    private void notifyProgress(long progressTime, long total) {
        if (PlayerPreconditions.checkNull(mIPlayerStateListener)) {
            return;
        }
        // Log.i(PlayerConstants.LOG_PROGRESS_TAG, "广播 直播通知 进度 : progressTime: " + progressTime + " total = "+ total);
        mIPlayerStateListener.onProgress(mPlayItem, (int) progressTime, (int) total);
    }

    private void notifyPause() {
        if (PlayerPreconditions.checkNull(mIPlayerStateListener)) {
            return;
        }
        mIPlayerStateListener.onPlayerPaused(mPlayItem);
    }

    private void notifyPlayEnd() {
        if (PlayerPreconditions.checkNull(mIPlayerStateListener)) {
            return;
        }
        PlayerLogUtil.log(TAG, "notifyPlayEnd");
        mIPlayerStateListener.onPlayerEnd(mPlayItem);
    }

    private void startTimer() {
        stopTimer();
        mDisposable = Flowable.interval(1, TimeUnit.SECONDS).onBackpressureDrop()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(aLong -> onTimer());
    }

    @Override
    public void reset(boolean needResetLastPlaybackRateFlag) {
        super.reset(needResetLastPlaybackRateFlag);
    }

    @Override
    public void rePlay() {
        super.rePlay();
    }

    @Override
    public void play() {
        Log.i(TAG, "=== IPlayControl play()");
        if (getPlayStatus() == PlayerConstants.TYPE_PLAYER_PAUSED && mPlayItem.getStatus() != PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
            rePlay();
        } else {
            super.play();
        }
    }

    @Override
    public void pause() {
        Log.i(TAG, "=== IPlayControl pause()");
        super.pause();
    }

    private void onTimer() {
        progressTime = progressTime + 1000;
        // Log.i(PlayerConstants.LOG_PROGRESS_TAG, "直播倒计时时间为: " + progressTime + " 总时间: " + livingTotalTime);
        if (progressTime > livingTotalTime) {
            ((BroadcastPlayItem) mPlayItem).setStatus(PlayerConstants.BROADCAST_STATUS_PLAYBACK);
            // 更新节目状态为可回放,清除过时的直播URL，避免数据不一致
            String oldUrl = mPlayItem.getPlayUrl();
            mPlayItem.setPlayUrl(null);
            PlayerLogUtil.log(TAG, "onTimer", "broadcast play end, status changed to PLAYBACK, clear old live url: " + oldUrl);
            mPlayItem.addMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_AUTO_PLAY_NEXT_ITEM, "1");
            if (isPlaying()) {
                mPlayItem.removeMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
            } else {
                PlayerLogUtil.log(TAG, "onTimer", "broadcast play end, is pause, loading data");
                mPlayItem.addMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY, "1");
            }
            notifyPlayEnd();
            stopTimer();
        } else {
            if (isPlaying()) {
                notifyProgress(progressTime, livingTotalTime);
            }
        }
    }


    public void stopTimer() {
        if (mDisposable != null && !mDisposable.isDisposed()) {
            mDisposable.dispose();
            mDisposable = null;
        }
    }


    @Override
    public void release() {
        super.release();
        stopTimer();
    }
}
