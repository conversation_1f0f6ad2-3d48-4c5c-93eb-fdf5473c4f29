<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.edog.car.ceshizhuanyong_kradio"
    android:versionCode="10800"
    android:versionName="1.8.0" >

    <uses-sdk
        android:minSdkVersion="23"
        android:targetSdkVersion="28" />

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <!-- 手机状态 -->
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />

    <application
        android:name="com.kaolafm.opensdk.demo.DemoApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:configChanges="orientation|screenSize|locale|layoutDirection|keyboard|screenLayout"
        android:extractNativeLibs="false"
        android:icon="@mipmap/ic_launcher_yt"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/AppTheme" >
        <activity
            android:name="com.kaolafm.opensdk.demo.emergency.EmergencyActivity"
            android:exported="false" />
        <activity
            android:name="com.kaolafm.opensdk.demo.recommed.RecommendActivity"
            android:exported="false" />
        <activity android:name="com.kaolafm.opensdk.demo.account.UserDurationActivity" />

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <activity android:name="com.kaolafm.opensdk.demo.MainActivity" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.kaolafm.opensdk.demo.detail.DetailActivity"
            android:label="@string/details" />
        <activity android:name="com.kaolafm.opensdk.demo.operation.column.ColumnActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.operation.column.ColumnDetailActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.operation.category.CategoryActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.login.LoginActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.search.VoiceSearchActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.search.SearchResultActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.search.ProgramDetailActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.operation.category.CategoryInfoActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.operation.category.CategoryMemberActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.subcribe.SubscribeActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.login.KaolaLoginActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.login.QQMusicLoginActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.live.LiveActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.qqmusic.CollectionActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.qqmusic.CollectionListActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.account.LinkAccountActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.qqmusic.QQMusicActivity" />

        <service android:name="com.kaolafm.opensdk.player.core.PlayerService" />

        <meta-data
            android:name="com.kaolafm.open.sdk.AppKey"
            android:value="f6dff42133bf06810a52a1d392b9906b" />
        <meta-data
            android:name="com.kaolafm.open.sdk.AppId"
            android:value="ye8192" />
        <meta-data
            android:name="com.kaolafm.open.sdk.Channel"
            android:value="ceshizhuanyong_kradio" />
        <meta-data
            android:name="com.kaolafm.open.sdk.CarType"
            android:value="xx5" />
        <!--
 <meta-data
             android:name="com.kaolafm.open.sdk.freeContent"
             android:value="true"
             />
        -->
        <!-- <meta-data -->
        <!-- android:name="com.kaolafm.ad.AppId" -->
        <!-- android:value="8fbf02809128e437bb29383a6f8d0e2a" -->
        <!-- /> -->
        <!--
             网易聊天室的 APP key, 可以在这里设置，也可以在 SDKOptions 中提供。
            如果 SDKOptions 中提供了，取 SDKOptions 中的值。
        -->
        <meta-data
            android:name="com.netease.nim.appKey"
            android:value="34e5e3c8a9a3e3a29e72c145ab70b5b2" />
        <meta-data
            android:name="com.kaolafm.open.sdk.qqmusic.AppId"
            android:value="12345700" />
        <meta-data
            android:name="com.kaolafm.open.sdk.qqmusic.AppKey"
            android:value="UyZaTlMrnqSJKNLsoy" />

        <activity android:name="com.kaolafm.opensdk.demo.brandinfo.BrandInfoActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.scene.SceneActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.player.RadioPlayerActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.player.BroadcastListPlayerActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.player.RadioPlayerGetListByAreaActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.player.BroadcastPlayerActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.player.BroadcastPlayerGetLocalByIdActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.player.AlbumPlayerActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.player.AudioPlayerActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.player.TVPlayerActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.player.QQMusicPlayerActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.history.HistoryActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.player.SubscribePlayerActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.search.KeywordSearchActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.personalise.InterestActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.personalise.UserActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.player.AudioDetailActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.DownloadActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.purchase.VipMealsActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.purchase.VipQRCodeActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.purchase.AlbumQRCodeActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.purchase.AudiosQRCodeActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.purchase.QRCodeStatusActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.purchase.BuyAlbumByCoinActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.purchase.BuyAudiosByCoinActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.purchase.PurchasedActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.purchase.OrderActivity" />
        <activity android:name="com.kaolafm.opensdk.demo.activity.ActivitiesActivity" />
        <!-- 声明云信后台服务 -->
        <service
            android:name="com.netease.nimlib.service.NimService"
            android:process=":core" /> <!-- 运行后台辅助服务 -->
        <service
            android:name="com.netease.nimlib.service.NimService$Aux"
            android:process=":core" /> <!-- 声明云信后台辅助服务 -->
        <service
            android:name="com.netease.nimlib.job.NIMJobService"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":core" /> <!-- 云信SDK的监视系统启动和网络变化的广播接收器，用户开机自启动以及网络变化时候重新登录 -->
        <receiver
            android:name="com.netease.nimlib.service.NimReceiver"
            android:exported="false"
            android:process=":core" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver> <!-- 云信进程间通信receiver -->
        <receiver android:name="com.netease.nimlib.service.ResponseReceiver" /> <!-- 云信进程间通信service -->
        <service android:name="com.netease.nimlib.service.ResponseService" />

        <provider
            android:name="androidx.lifecycle.ProcessLifecycleOwnerInitializer"
            android:authorities="com.edog.car.ceshizhuanyong_kradio.lifecycle-process"
            android:exported="false"
            android:multiprocess="true" />

        <service
            android:name="com.kaolafm.ad.timer.TimerJobService"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />
    </application>

</manifest>