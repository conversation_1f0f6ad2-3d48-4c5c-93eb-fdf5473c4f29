-- Merging decision tree log ---
meta-data#com.kaolafm.open.sdk.AppKey
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:71:9-73:42
	android:value
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:73:13-39
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:72:13-55
meta-data#com.kaolafm.open.sdk.AppId
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:74:9-76:41
	android:value
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:76:13-38
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:75:13-54
meta-data#com.kaolafm.open.sdk.Channel
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:77:9-79:42
	android:value
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:79:13-39
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:78:13-56
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:2:1-137:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:2:1-137:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:2:1-137:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:2:1-137:12
MERGED from [:sdk] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\sdk\build\intermediates\library_manifest\release\AndroidManifest.xml:2:1-15:12
MERGED from [com.github.tbruyelle:rxpermissions:v0.11] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\0da2d4ed05bc5d3a107e82db91677dd4\jetified-rxpermissions-v0.11\AndroidManifest.xml:2:1-11:12
MERGED from [com.trello.rxlifecycle3:rxlifecycle-components:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\f28ac88ce129fa6830ba71ab5f7a6db3\jetified-rxlifecycle-components-3.1.0\AndroidManifest.xml:13:1-18:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\4a7f5220c5722fc8f5ef9e9727215758\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\43e13e1514558d13002012daba640166\constraintlayout-1.1.3\AndroidManifest.xml:2:1-11:12
MERGED from [com.lcodecorex:tkrefreshlayout:1.0.7] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b0d45b121b2647db08cdfb2494909ba6\jetified-tkrefreshlayout-1.0.7\AndroidManifest.xml:2:1-17:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\61680fb4a271c1d4e88bc37adaba525d\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\88a1d4513295438135310afef65e118f\jetified-glide-4.11.0\AndroidManifest.xml:2:1-12:12
MERGED from [com.jakewharton:butterknife:10.2.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\93ae6e3fff968443fddb0a47a97de1e2\jetified-butterknife-10.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:2:1-63:12
MERGED from [com.netease.nimlib:chatroom:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\4bf665f41026dacee657316474c6d441\jetified-chatroom-5.1.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.elvishew:xlog:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\3e0138bd4cbf10fcb059936a9470f488\jetified-xlog-1.6.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.orhanobut:logger:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\abd45c448d5d6fb3941765220ee3c353\jetified-logger-2.2.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.trello.rxlifecycle3:rxlifecycle-android:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\ca83bbb4136c50b055c86b4d2f7c8995\jetified-rxlifecycle-android-3.1.0\AndroidManifest.xml:13:1-18:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\3d060b035b8f17dac2ad6734ad9c37a3\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\9d215733c6223810f748caa85622b789\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\9d8a4cd8e21a14b97d0ea5bcafe7c3c5\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\3dfb6b10885b6a6a854c822a908d3ee5\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\f8cdbeb883c00046aacac8b39082e805\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.jakewharton:butterknife-runtime:10.2.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\da1fa2b03507b1c5d68b2261be9bc76a\jetified-butterknife-runtime-10.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [:socket] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\socket\build\intermediates\library_manifest\release\AndroidManifest.xml:2:1-11:12
MERGED from [:player] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\player\build\intermediates\library_manifest\release\AndroidManifest.xml:2:1-13:12
MERGED from [:api] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\api\build\intermediates\library_manifest\release\AndroidManifest.xml:2:1-13:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\acf82ecfc83131f95c3d459a33d58b07\lifecycle-extensions-2.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.fragment:fragment:1.2.5] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\88afcf77c9f945c4551c128d01d9880b\fragment-1.2.5\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\d3e5309bb5773ea4813cad47b1c3809f\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\6be1f59b9675c01c327ea768fba32399\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\ef02ef8c97780c5ce59861ad1ae89f15\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\28901321bf70a14181739121c863a409\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c65561dc926cd1203d0984bb04e4d461\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c86f5fc4a23ef091d86066cb995b5164\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\f81f11b3f447972e60813fabcff318e6\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\cfc12adc1e39cffa1ca5c0a1af0c70fd\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b52a58aa6ea8158ac783966c4e4b6400\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c03fe72d5231576c504b787f06aff13d\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\1355f8c0d42bb662bca608c6d95aee25\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\80bb9695279b51939e0c8ecd4b784fea\jetified-activity-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\5d415039a9196d7dcbbfb2b7a48427a4\core-1.1.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\89f24cd9381ae2e9a7a567f64c27fcff\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\ee3e63c05d1eeff561ea6777be2f41a8\jetified-gifdecoder-4.11.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b9be8d46721a0dd82e24b3af4e9f2e1b\exifinterface-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c088e46a6aa824f7af5801e67040d0c6\jetified-lifecycle-process-2.2.0\AndroidManifest.xml:17:1-32:12
MERGED from [androidx.lifecycle:lifecycle-service:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\82dc50e2d61c08b3f64d17bca6fdf8b8\jetified-lifecycle-service-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\cf411204873ab8bb1dbda4fad57b468b\lifecycle-runtime-2.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [:ad] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\ad\build\intermediates\library_manifest\release\AndroidManifest.xml:2:1-21:12
MERGED from [:core] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\core\build\intermediates\library_manifest\release\AndroidManifest.xml:2:1-15:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\ae76e94cee7b4d93db63d4550e377618\versionedparcelable-1.1.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\81f357388aeb0d6a8815f20ab446efc2\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\d325d97cbb68f8ed8b6ed211b38f245b\jetified-lifecycle-viewmodel-savedstate-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\94fb8ed682724e8d1458ba7e01372dc1\lifecycle-viewmodel-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\05fa4f8a63dfbec0e4a1e84cb855d098\lifecycle-livedata-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\25cd320baae6f818710b4cde7a6a63a5\lifecycle-livedata-core-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-rxjava2:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b91325c7cb38fb82ee82275f7282c128\room-rxjava2-2.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-runtime:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\958824cfaac20f2d3074778062c67621\room-runtime-2.4.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\5a5528ced108e6fcfa4dbb22b29cdf08\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\463529171dbab1b571704b66872565a8\jetified-savedstate-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\5dc5f4d2d2aa2225dec1adbaf55dc24e\sqlite-framework-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b4c1fd6aeddfd75f5d6191ef8e9c0df3\sqlite-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\a5537d3223ad5108b364a22e893b3d19\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\12820597a0368d764c0ba87ec92e71cf\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b83db2c95252edd19be8bdc9a7a212b5\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.reactivex.rxjava2:rxandroid:2.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b7e7101e3a661d7b890e7c2fe3cf3682\jetified-rxandroid-2.1.1\AndroidManifest.xml:15:1-20:12
MERGED from [:utils] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\build\intermediates\library_manifest\release\AndroidManifest.xml:2:1-15:12
MERGED from [:adreport] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\adreport\build\intermediates\library_manifest\release\AndroidManifest.xml:2:1-15:12
MERGED from [:report] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\report\build\intermediates\library_manifest\release\AndroidManifest.xml:2:1-13:12
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\118429545f33667267e89cf74388ca16\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:17:1-24:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:2:1-137:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:2:1-137:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:2:1-137:12
	package
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:4:5-39
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:2:1-137:12
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:2:1-137:12
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:6:5-79
MERGED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:15:5-79
MERGED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:15:5-79
MERGED from [:player] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\player\build\intermediates\library_manifest\release\AndroidManifest.xml:11:5-79
MERGED from [:player] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\player\build\intermediates\library_manifest\release\AndroidManifest.xml:11:5-79
MERGED from [:ad] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\ad\build\intermediates\library_manifest\release\AndroidManifest.xml:11:5-79
MERGED from [:ad] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\ad\build\intermediates\library_manifest\release\AndroidManifest.xml:11:5-79
MERGED from [:core] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\core\build\intermediates\library_manifest\release\AndroidManifest.xml:11:5-79
MERGED from [:core] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\core\build\intermediates\library_manifest\release\AndroidManifest.xml:11:5-79
MERGED from [:utils] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\build\intermediates\library_manifest\release\AndroidManifest.xml:11:5-79
MERGED from [:utils] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\build\intermediates\library_manifest\release\AndroidManifest.xml:11:5-79
MERGED from [:adreport] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\adreport\build\intermediates\library_manifest\release\AndroidManifest.xml:12:5-79
MERGED from [:adreport] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\adreport\build\intermediates\library_manifest\release\AndroidManifest.xml:12:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:7:5-76
MERGED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:17:5-76
MERGED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:17:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:7:22-73
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:8:5-76
MERGED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:16:5-76
MERGED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:16:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.CHANGE_WIFI_MULTICAST_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:9:5-86
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:9:22-83
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:10:5-67
MERGED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:14:5-67
MERGED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:14:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:10:22-64
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:11:5-75
MERGED from [:sdk] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\sdk\build\intermediates\library_manifest\release\AndroidManifest.xml:13:5-75
MERGED from [:sdk] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\sdk\build\intermediates\library_manifest\release\AndroidManifest.xml:13:5-75
MERGED from [:api] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\api\build\intermediates\library_manifest\release\AndroidManifest.xml:11:5-75
MERGED from [:api] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\api\build\intermediates\library_manifest\release\AndroidManifest.xml:11:5-75
MERGED from [:adreport] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\adreport\build\intermediates\library_manifest\release\AndroidManifest.xml:13:5-75
MERGED from [:adreport] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\adreport\build\intermediates\library_manifest\release\AndroidManifest.xml:13:5-75
MERGED from [:report] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\report\build\intermediates\library_manifest\release\AndroidManifest.xml:11:5-75
MERGED from [:report] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\report\build\intermediates\library_manifest\release\AndroidManifest.xml:11:5-75
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:11:22-72
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:12:5-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:12:22-65
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:13:5-81
MERGED from [:sdk] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\sdk\build\intermediates\library_manifest\release\AndroidManifest.xml:11:5-81
MERGED from [:sdk] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\sdk\build\intermediates\library_manifest\release\AndroidManifest.xml:11:5-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:13:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:14:5-79
MERGED from [:sdk] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\sdk\build\intermediates\library_manifest\release\AndroidManifest.xml:12:5-79
MERGED from [:sdk] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\sdk\build\intermediates\library_manifest\release\AndroidManifest.xml:12:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:14:22-76
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:15:5-81
MERGED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:24:5-81
MERGED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:24:5-81
MERGED from [:ad] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\ad\build\intermediates\library_manifest\release\AndroidManifest.xml:13:5-81
MERGED from [:ad] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\ad\build\intermediates\library_manifest\release\AndroidManifest.xml:13:5-81
MERGED from [:core] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\core\build\intermediates\library_manifest\release\AndroidManifest.xml:13:5-81
MERGED from [:core] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\core\build\intermediates\library_manifest\release\AndroidManifest.xml:13:5-81
MERGED from [:utils] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\build\intermediates\library_manifest\release\AndroidManifest.xml:13:5-81
MERGED from [:utils] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\build\intermediates\library_manifest\release\AndroidManifest.xml:13:5-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:15:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:16:5-80
MERGED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:23:5-80
MERGED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:23:5-80
MERGED from [:ad] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\ad\build\intermediates\library_manifest\release\AndroidManifest.xml:12:5-80
MERGED from [:ad] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\ad\build\intermediates\library_manifest\release\AndroidManifest.xml:12:5-80
MERGED from [:core] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\core\build\intermediates\library_manifest\release\AndroidManifest.xml:12:5-80
MERGED from [:core] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\core\build\intermediates\library_manifest\release\AndroidManifest.xml:12:5-80
MERGED from [:utils] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\build\intermediates\library_manifest\release\AndroidManifest.xml:12:5-80
MERGED from [:utils] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\build\intermediates\library_manifest\release\AndroidManifest.xml:12:5-80
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:16:22-77
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:17:5-71
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:17:22-68
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:19:5-135:19
MERGED from [com.github.tbruyelle:rxpermissions:v0.11] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\0da2d4ed05bc5d3a107e82db91677dd4\jetified-rxpermissions-v0.11\AndroidManifest.xml:9:5-20
MERGED from [com.github.tbruyelle:rxpermissions:v0.11] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\0da2d4ed05bc5d3a107e82db91677dd4\jetified-rxpermissions-v0.11\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\43e13e1514558d13002012daba640166\constraintlayout-1.1.3\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\43e13e1514558d13002012daba640166\constraintlayout-1.1.3\AndroidManifest.xml:9:5-20
MERGED from [com.lcodecorex:tkrefreshlayout:1.0.7] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b0d45b121b2647db08cdfb2494909ba6\jetified-tkrefreshlayout-1.0.7\AndroidManifest.xml:11:5-15:19
MERGED from [com.lcodecorex:tkrefreshlayout:1.0.7] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b0d45b121b2647db08cdfb2494909ba6\jetified-tkrefreshlayout-1.0.7\AndroidManifest.xml:11:5-15:19
MERGED from [com.github.bumptech.glide:glide:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\88a1d4513295438135310afef65e118f\jetified-glide-4.11.0\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\88a1d4513295438135310afef65e118f\jetified-glide-4.11.0\AndroidManifest.xml:10:5-20
MERGED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:26:5-61:19
MERGED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:26:5-61:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\acf82ecfc83131f95c3d459a33d58b07\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\acf82ecfc83131f95c3d459a33d58b07\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.core:core:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\5d415039a9196d7dcbbfb2b7a48427a4\core-1.1.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\5d415039a9196d7dcbbfb2b7a48427a4\core-1.1.0\AndroidManifest.xml:24:5-89
MERGED from [com.github.bumptech.glide:gifdecoder:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\ee3e63c05d1eeff561ea6777be2f41a8\jetified-gifdecoder-4.11.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\ee3e63c05d1eeff561ea6777be2f41a8\jetified-gifdecoder-4.11.0\AndroidManifest.xml:9:5-20
MERGED from [androidx.lifecycle:lifecycle-process:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c088e46a6aa824f7af5801e67040d0c6\jetified-lifecycle-process-2.2.0\AndroidManifest.xml:24:5-30:19
MERGED from [androidx.lifecycle:lifecycle-process:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c088e46a6aa824f7af5801e67040d0c6\jetified-lifecycle-process-2.2.0\AndroidManifest.xml:24:5-30:19
MERGED from [:ad] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\ad\build\intermediates\library_manifest\release\AndroidManifest.xml:15:5-19:19
MERGED from [:ad] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\ad\build\intermediates\library_manifest\release\AndroidManifest.xml:15:5-19:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\ae76e94cee7b4d93db63d4550e377618\versionedparcelable-1.1.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\ae76e94cee7b4d93db63d4550e377618\versionedparcelable-1.1.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.room:room-runtime:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\958824cfaac20f2d3074778062c67621\room-runtime-2.4.1\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\958824cfaac20f2d3074778062c67621\room-runtime-2.4.1\AndroidManifest.xml:24:5-29:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\5d415039a9196d7dcbbfb2b7a48427a4\core-1.1.0\AndroidManifest.xml:24:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:26:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:24:9-41
	android:configChanges
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:22:9-100
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:28:9-28
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:23:9-46
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:21:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:27:9-40
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:25:9-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:20:9-40
activity#com.kaolafm.opensdk.demo.emergency.EmergencyActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:29:9-31:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:31:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:30:13-56
activity#com.kaolafm.opensdk.demo.recommed.RecommendActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:34:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:33:13-55
activity#com.kaolafm.opensdk.demo.account.UserDurationActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:35:9-66
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:35:19-63
uses-library#org.apache.http.legacy
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:37:9-39:40
	android:required
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:39:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:38:13-50
activity#com.kaolafm.opensdk.demo.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:41:9-47:20
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:41:19-47
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:42:13-46:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:43:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:43:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:45:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:45:27-74
activity#com.kaolafm.opensdk.demo.detail.DetailActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:48:9-50:47
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:50:13-44
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:49:13-50
activity#com.kaolafm.opensdk.demo.operation.column.ColumnActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:51:9-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:51:19-66
activity#com.kaolafm.opensdk.demo.operation.column.ColumnDetailActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:52:9-75
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:52:19-72
activity#com.kaolafm.opensdk.demo.operation.category.CategoryActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:53:9-73
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:53:19-70
activity#com.kaolafm.opensdk.demo.login.LoginActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:54:9-57
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:54:19-54
activity#com.kaolafm.opensdk.demo.search.VoiceSearchActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:55:9-64
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:55:19-61
activity#com.kaolafm.opensdk.demo.search.SearchResultActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:56:9-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:56:19-62
activity#com.kaolafm.opensdk.demo.search.ProgramDetailActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:57:9-66
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:57:19-63
activity#com.kaolafm.opensdk.demo.operation.category.CategoryInfoActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:58:9-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:58:19-74
activity#com.kaolafm.opensdk.demo.operation.category.CategoryMemberActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:59:9-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:59:19-76
activity#com.kaolafm.opensdk.demo.subcribe.SubscribeActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:60:9-64
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:60:19-61
activity#com.kaolafm.opensdk.demo.login.KaolaLoginActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:61:9-62
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:61:19-59
activity#com.kaolafm.opensdk.demo.login.QQMusicLoginActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:62:9-64
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:62:19-61
activity#com.kaolafm.opensdk.demo.live.LiveActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:63:9-55
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:63:19-52
activity#com.kaolafm.opensdk.demo.qqmusic.CollectionActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:64:9-64
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:64:19-61
activity#com.kaolafm.opensdk.demo.qqmusic.CollectionListActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:65:9-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:65:19-65
activity#com.kaolafm.opensdk.demo.account.LinkAccountActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:66:9-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:66:19-62
activity#com.kaolafm.opensdk.demo.qqmusic.QQMusicActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:67:9-61
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:67:19-58
service#com.kaolafm.opensdk.player.core.PlayerService
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:69:9-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:69:18-78
meta-data#com.kaolafm.open.sdk.CarType
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:80:9-82:35
	android:value
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:82:13-32
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:81:13-56
meta-data#com.netease.nim.appKey
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:97:9-99:64
	android:value
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:99:13-61
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:98:13-50
meta-data#com.kaolafm.open.sdk.qqmusic.AppId
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:100:9-102:40
	android:value
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:102:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:101:13-62
meta-data#com.kaolafm.open.sdk.qqmusic.AppKey
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:103:9-105:50
	android:value
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:105:13-47
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:104:13-63
activity#com.kaolafm.opensdk.demo.brandinfo.BrandInfoActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:107:9-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:107:19-62
activity#com.kaolafm.opensdk.demo.scene.SceneActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:108:9-57
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:108:19-54
activity#com.kaolafm.opensdk.demo.player.RadioPlayerActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:109:9-64
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:109:19-61
activity#com.kaolafm.opensdk.demo.player.BroadcastListPlayerActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:110:9-72
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:110:19-69
activity#com.kaolafm.opensdk.demo.player.RadioPlayerGetListByAreaActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:111:9-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:111:19-74
activity#com.kaolafm.opensdk.demo.player.BroadcastPlayerActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:112:9-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:112:19-65
activity#com.kaolafm.opensdk.demo.player.BroadcastPlayerGetLocalByIdActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:113:9-80
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:113:19-77
activity#com.kaolafm.opensdk.demo.player.AlbumPlayerActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:114:9-64
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:114:19-61
activity#com.kaolafm.opensdk.demo.player.AudioPlayerActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:115:9-64
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:115:19-61
activity#com.kaolafm.opensdk.demo.player.TVPlayerActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:116:9-61
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:116:19-58
activity#com.kaolafm.opensdk.demo.player.QQMusicPlayerActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:117:9-66
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:117:19-63
activity#com.kaolafm.opensdk.demo.history.HistoryActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:118:9-61
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:118:19-58
activity#com.kaolafm.opensdk.demo.player.SubscribePlayerActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:119:9-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:119:19-65
activity#com.kaolafm.opensdk.demo.search.KeywordSearchActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:120:9-66
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:120:19-63
activity#com.kaolafm.opensdk.demo.personalise.InterestActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:121:9-66
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:121:19-63
activity#com.kaolafm.opensdk.demo.personalise.UserActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:122:9-62
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:122:19-59
activity#com.kaolafm.opensdk.demo.player.AudioDetailActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:123:9-64
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:123:19-61
activity#com.kaolafm.opensdk.demo.DownloadActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:124:9-54
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:124:19-51
activity#com.kaolafm.opensdk.demo.purchase.VipMealsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:125:9-63
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:125:19-60
activity#com.kaolafm.opensdk.demo.purchase.VipQRCodeActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:126:9-64
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:126:19-61
activity#com.kaolafm.opensdk.demo.purchase.AlbumQRCodeActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:127:9-66
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:127:19-63
activity#com.kaolafm.opensdk.demo.purchase.AudiosQRCodeActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:128:9-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:128:19-64
activity#com.kaolafm.opensdk.demo.purchase.QRCodeStatusActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:129:9-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:129:19-64
activity#com.kaolafm.opensdk.demo.purchase.BuyAlbumByCoinActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:130:9-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:130:19-66
activity#com.kaolafm.opensdk.demo.purchase.BuyAudiosByCoinActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:131:9-70
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:131:19-67
activity#com.kaolafm.opensdk.demo.purchase.PurchasedActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:132:9-64
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:132:19-61
activity#com.kaolafm.opensdk.demo.purchase.OrderActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:133:9-60
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:133:19-57
activity#com.kaolafm.opensdk.demo.activity.ActivitiesActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:134:9-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:134:19-62
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
MERGED from [:sdk] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\sdk\build\intermediates\library_manifest\release\AndroidManifest.xml:7:5-9:41
MERGED from [:sdk] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\sdk\build\intermediates\library_manifest\release\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.tbruyelle:rxpermissions:v0.11] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\0da2d4ed05bc5d3a107e82db91677dd4\jetified-rxpermissions-v0.11\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.tbruyelle:rxpermissions:v0.11] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\0da2d4ed05bc5d3a107e82db91677dd4\jetified-rxpermissions-v0.11\AndroidManifest.xml:5:5-7:41
MERGED from [com.trello.rxlifecycle3:rxlifecycle-components:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\f28ac88ce129fa6830ba71ab5f7a6db3\jetified-rxlifecycle-components-3.1.0\AndroidManifest.xml:16:5-44
MERGED from [com.trello.rxlifecycle3:rxlifecycle-components:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\f28ac88ce129fa6830ba71ab5f7a6db3\jetified-rxlifecycle-components-3.1.0\AndroidManifest.xml:16:5-44
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\4a7f5220c5722fc8f5ef9e9727215758\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\4a7f5220c5722fc8f5ef9e9727215758\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\43e13e1514558d13002012daba640166\constraintlayout-1.1.3\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\43e13e1514558d13002012daba640166\constraintlayout-1.1.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.lcodecorex:tkrefreshlayout:1.0.7] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b0d45b121b2647db08cdfb2494909ba6\jetified-tkrefreshlayout-1.0.7\AndroidManifest.xml:7:5-9:41
MERGED from [com.lcodecorex:tkrefreshlayout:1.0.7] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b0d45b121b2647db08cdfb2494909ba6\jetified-tkrefreshlayout-1.0.7\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\61680fb4a271c1d4e88bc37adaba525d\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\61680fb4a271c1d4e88bc37adaba525d\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\88a1d4513295438135310afef65e118f\jetified-glide-4.11.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\88a1d4513295438135310afef65e118f\jetified-glide-4.11.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.jakewharton:butterknife:10.2.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\93ae6e3fff968443fddb0a47a97de1e2\jetified-butterknife-10.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton:butterknife:10.2.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\93ae6e3fff968443fddb0a47a97de1e2\jetified-butterknife-10.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:8:5-11:91
MERGED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:8:5-11:91
MERGED from [com.netease.nimlib:chatroom:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\4bf665f41026dacee657316474c6d441\jetified-chatroom-5.1.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.netease.nimlib:chatroom:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\4bf665f41026dacee657316474c6d441\jetified-chatroom-5.1.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.elvishew:xlog:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\3e0138bd4cbf10fcb059936a9470f488\jetified-xlog-1.6.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.elvishew:xlog:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\3e0138bd4cbf10fcb059936a9470f488\jetified-xlog-1.6.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.orhanobut:logger:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\abd45c448d5d6fb3941765220ee3c353\jetified-logger-2.2.0\AndroidManifest.xml:6:5-43
MERGED from [com.orhanobut:logger:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\abd45c448d5d6fb3941765220ee3c353\jetified-logger-2.2.0\AndroidManifest.xml:6:5-43
MERGED from [com.trello.rxlifecycle3:rxlifecycle-android:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\ca83bbb4136c50b055c86b4d2f7c8995\jetified-rxlifecycle-android-3.1.0\AndroidManifest.xml:16:5-44
MERGED from [com.trello.rxlifecycle3:rxlifecycle-android:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\ca83bbb4136c50b055c86b4d2f7c8995\jetified-rxlifecycle-android-3.1.0\AndroidManifest.xml:16:5-44
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\3d060b035b8f17dac2ad6734ad9c37a3\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\3d060b035b8f17dac2ad6734ad9c37a3\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\9d215733c6223810f748caa85622b789\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\9d215733c6223810f748caa85622b789\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\9d8a4cd8e21a14b97d0ea5bcafe7c3c5\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\9d8a4cd8e21a14b97d0ea5bcafe7c3c5\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\3dfb6b10885b6a6a854c822a908d3ee5\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\3dfb6b10885b6a6a854c822a908d3ee5\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\f8cdbeb883c00046aacac8b39082e805\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\f8cdbeb883c00046aacac8b39082e805\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.jakewharton:butterknife-runtime:10.2.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\da1fa2b03507b1c5d68b2261be9bc76a\jetified-butterknife-runtime-10.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton:butterknife-runtime:10.2.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\da1fa2b03507b1c5d68b2261be9bc76a\jetified-butterknife-runtime-10.2.1\AndroidManifest.xml:5:5-44
MERGED from [:socket] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\socket\build\intermediates\library_manifest\release\AndroidManifest.xml:7:5-9:41
MERGED from [:socket] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\socket\build\intermediates\library_manifest\release\AndroidManifest.xml:7:5-9:41
MERGED from [:player] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\player\build\intermediates\library_manifest\release\AndroidManifest.xml:7:5-9:41
MERGED from [:player] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\player\build\intermediates\library_manifest\release\AndroidManifest.xml:7:5-9:41
MERGED from [:api] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\api\build\intermediates\library_manifest\release\AndroidManifest.xml:7:5-9:41
MERGED from [:api] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\api\build\intermediates\library_manifest\release\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\acf82ecfc83131f95c3d459a33d58b07\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\acf82ecfc83131f95c3d459a33d58b07\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.2.5] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\88afcf77c9f945c4551c128d01d9880b\fragment-1.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.2.5] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\88afcf77c9f945c4551c128d01d9880b\fragment-1.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\d3e5309bb5773ea4813cad47b1c3809f\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\d3e5309bb5773ea4813cad47b1c3809f\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\6be1f59b9675c01c327ea768fba32399\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\6be1f59b9675c01c327ea768fba32399\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\ef02ef8c97780c5ce59861ad1ae89f15\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\ef02ef8c97780c5ce59861ad1ae89f15\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\28901321bf70a14181739121c863a409\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\28901321bf70a14181739121c863a409\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c65561dc926cd1203d0984bb04e4d461\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c65561dc926cd1203d0984bb04e4d461\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c86f5fc4a23ef091d86066cb995b5164\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c86f5fc4a23ef091d86066cb995b5164\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\f81f11b3f447972e60813fabcff318e6\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\f81f11b3f447972e60813fabcff318e6\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\cfc12adc1e39cffa1ca5c0a1af0c70fd\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\cfc12adc1e39cffa1ca5c0a1af0c70fd\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b52a58aa6ea8158ac783966c4e4b6400\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b52a58aa6ea8158ac783966c4e4b6400\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c03fe72d5231576c504b787f06aff13d\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c03fe72d5231576c504b787f06aff13d\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\1355f8c0d42bb662bca608c6d95aee25\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\1355f8c0d42bb662bca608c6d95aee25\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\80bb9695279b51939e0c8ecd4b784fea\jetified-activity-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\80bb9695279b51939e0c8ecd4b784fea\jetified-activity-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\5d415039a9196d7dcbbfb2b7a48427a4\core-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\5d415039a9196d7dcbbfb2b7a48427a4\core-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\89f24cd9381ae2e9a7a567f64c27fcff\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\89f24cd9381ae2e9a7a567f64c27fcff\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\ee3e63c05d1eeff561ea6777be2f41a8\jetified-gifdecoder-4.11.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.11.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\ee3e63c05d1eeff561ea6777be2f41a8\jetified-gifdecoder-4.11.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b9be8d46721a0dd82e24b3af4e9f2e1b\exifinterface-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b9be8d46721a0dd82e24b3af4e9f2e1b\exifinterface-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c088e46a6aa824f7af5801e67040d0c6\jetified-lifecycle-process-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-process:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c088e46a6aa824f7af5801e67040d0c6\jetified-lifecycle-process-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-service:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\82dc50e2d61c08b3f64d17bca6fdf8b8\jetified-lifecycle-service-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-service:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\82dc50e2d61c08b3f64d17bca6fdf8b8\jetified-lifecycle-service-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\cf411204873ab8bb1dbda4fad57b468b\lifecycle-runtime-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\cf411204873ab8bb1dbda4fad57b468b\lifecycle-runtime-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [:ad] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\ad\build\intermediates\library_manifest\release\AndroidManifest.xml:7:5-9:41
MERGED from [:ad] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\ad\build\intermediates\library_manifest\release\AndroidManifest.xml:7:5-9:41
MERGED from [:core] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\core\build\intermediates\library_manifest\release\AndroidManifest.xml:7:5-9:41
MERGED from [:core] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\core\build\intermediates\library_manifest\release\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\ae76e94cee7b4d93db63d4550e377618\versionedparcelable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\ae76e94cee7b4d93db63d4550e377618\versionedparcelable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\81f357388aeb0d6a8815f20ab446efc2\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\81f357388aeb0d6a8815f20ab446efc2\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\d325d97cbb68f8ed8b6ed211b38f245b\jetified-lifecycle-viewmodel-savedstate-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\d325d97cbb68f8ed8b6ed211b38f245b\jetified-lifecycle-viewmodel-savedstate-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\94fb8ed682724e8d1458ba7e01372dc1\lifecycle-viewmodel-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\94fb8ed682724e8d1458ba7e01372dc1\lifecycle-viewmodel-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\05fa4f8a63dfbec0e4a1e84cb855d098\lifecycle-livedata-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\05fa4f8a63dfbec0e4a1e84cb855d098\lifecycle-livedata-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\25cd320baae6f818710b4cde7a6a63a5\lifecycle-livedata-core-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\25cd320baae6f818710b4cde7a6a63a5\lifecycle-livedata-core-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-rxjava2:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b91325c7cb38fb82ee82275f7282c128\room-rxjava2-2.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-rxjava2:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b91325c7cb38fb82ee82275f7282c128\room-rxjava2-2.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\958824cfaac20f2d3074778062c67621\room-runtime-2.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\958824cfaac20f2d3074778062c67621\room-runtime-2.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\5a5528ced108e6fcfa4dbb22b29cdf08\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\5a5528ced108e6fcfa4dbb22b29cdf08\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\463529171dbab1b571704b66872565a8\jetified-savedstate-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\463529171dbab1b571704b66872565a8\jetified-savedstate-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\5dc5f4d2d2aa2225dec1adbaf55dc24e\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\5dc5f4d2d2aa2225dec1adbaf55dc24e\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b4c1fd6aeddfd75f5d6191ef8e9c0df3\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b4c1fd6aeddfd75f5d6191ef8e9c0df3\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\a5537d3223ad5108b364a22e893b3d19\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\a5537d3223ad5108b364a22e893b3d19\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\12820597a0368d764c0ba87ec92e71cf\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\12820597a0368d764c0ba87ec92e71cf\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b83db2c95252edd19be8bdc9a7a212b5\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b83db2c95252edd19be8bdc9a7a212b5\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [io.reactivex.rxjava2:rxandroid:2.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b7e7101e3a661d7b890e7c2fe3cf3682\jetified-rxandroid-2.1.1\AndroidManifest.xml:18:5-43
MERGED from [io.reactivex.rxjava2:rxandroid:2.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\b7e7101e3a661d7b890e7c2fe3cf3682\jetified-rxandroid-2.1.1\AndroidManifest.xml:18:5-43
MERGED from [:utils] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\build\intermediates\library_manifest\release\AndroidManifest.xml:7:5-9:41
MERGED from [:utils] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\build\intermediates\library_manifest\release\AndroidManifest.xml:7:5-9:41
MERGED from [:adreport] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\adreport\build\intermediates\library_manifest\release\AndroidManifest.xml:7:5-9:41
MERGED from [:adreport] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\adreport\build\intermediates\library_manifest\release\AndroidManifest.xml:7:5-9:41
MERGED from [:report] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\report\build\intermediates\library_manifest\release\AndroidManifest.xml:7:5-9:41
MERGED from [:report] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\report\build\intermediates\library_manifest\release\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\118429545f33667267e89cf74388ca16\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\118429545f33667267e89cf74388ca16\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
	tools:overrideLibrary
		ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:11:9-88
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
uses-permission#android.permission.GET_TASKS
ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:20:5-68
	android:name
		ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:20:22-65
service#com.netease.nimlib.service.NimService
ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:29:9-31:39
	android:process
		ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:31:13-36
	android:name
		ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:30:13-65
service#com.netease.nimlib.service.NimService$Aux
ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:34:9-36:39
	android:process
		ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:36:13-36
	android:name
		ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:35:13-69
service#com.netease.nimlib.job.NIMJobService
ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:39:9-43:39
	android:process
		ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:43:13-36
	android:exported
		ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:41:13-36
	android:permission
		ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:42:13-69
	android:name
		ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:40:13-64
receiver#com.netease.nimlib.service.NimReceiver
ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:46:9-54:20
	android:process
		ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:49:13-36
	android:exported
		ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:48:13-37
	android:name
		ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:47:13-66
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:50:13-53:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:51:17-79
	android:name
		ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:51:25-76
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:52:17-79
	android:name
		ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:52:25-76
receiver#com.netease.nimlib.service.ResponseReceiver
ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:57:9-80
	android:name
		ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:57:19-77
service#com.netease.nimlib.service.ResponseService
ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:60:9-78
	android:name
		ADDED from [com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:60:18-75
provider#androidx.lifecycle.ProcessLifecycleOwnerInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c088e46a6aa824f7af5801e67040d0c6\jetified-lifecycle-process-2.2.0\AndroidManifest.xml:25:9-29:43
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c088e46a6aa824f7af5801e67040d0c6\jetified-lifecycle-process-2.2.0\AndroidManifest.xml:27:13-69
	android:multiprocess
		ADDED from [androidx.lifecycle:lifecycle-process:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c088e46a6aa824f7af5801e67040d0c6\jetified-lifecycle-process-2.2.0\AndroidManifest.xml:29:13-40
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c088e46a6aa824f7af5801e67040d0c6\jetified-lifecycle-process-2.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c088e46a6aa824f7af5801e67040d0c6\jetified-lifecycle-process-2.2.0\AndroidManifest.xml:26:13-79
service#com.kaolafm.ad.timer.TimerJobService
ADDED from [:ad] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\ad\build\intermediates\library_manifest\release\AndroidManifest.xml:16:9-18:72
	android:permission
		ADDED from [:ad] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\ad\build\intermediates\library_manifest\release\AndroidManifest.xml:18:13-69
	android:name
		ADDED from [:ad] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\ad\build\intermediates\library_manifest\release\AndroidManifest.xml:17:13-64
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\958824cfaac20f2d3074778062c67621\room-runtime-2.4.1\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\958824cfaac20f2d3074778062c67621\room-runtime-2.4.1\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\958824cfaac20f2d3074778062c67621\room-runtime-2.4.1\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\958824cfaac20f2d3074778062c67621\room-runtime-2.4.1\AndroidManifest.xml:26:13-74
uses-permission#android.permission.READ_PRIVILEGED_PHONE_STATE
ADDED from [:adreport] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\adreport\build\intermediates\library_manifest\release\AndroidManifest.xml:11:5-86
	android:name
		ADDED from [:adreport] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\adreport\build\intermediates\library_manifest\release\AndroidManifest.xml:11:22-83
