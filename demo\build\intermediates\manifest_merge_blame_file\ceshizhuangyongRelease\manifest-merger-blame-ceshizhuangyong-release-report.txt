1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.edog.car.ceshizhuanyong_kradio"
4    android:versionCode="10800"
5    android:versionName="1.8.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
8-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
9        android:targetSdkVersion="28" />
9-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
11-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:6:5-79
11-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:6:22-76
12    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
12-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:7:5-76
12-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:7:22-73
13    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
13-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:8:5-76
13-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:8:22-73
14    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
14-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:9:5-86
14-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:9:22-83
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:10:5-67
15-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:10:22-64
16    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
16-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:11:5-75
16-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:11:22-72
17    <uses-permission android:name="android.permission.WAKE_LOCK" />
17-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:12:5-68
17-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:12:22-65
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
18-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:13:5-81
18-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:13:22-78
19    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
19-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:14:5-79
19-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:14:22-76
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:15:5-81
20-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:15:22-78
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:16:5-80
21-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:16:22-77
22    <uses-permission android:name="android.permission.RECORD_AUDIO" />
22-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:17:5-71
22-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:17:22-68
23
24    <!-- 手机状态 -->
25    <uses-permission android:name="android.permission.GET_TASKS" />
25-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:20:5-68
25-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:20:22-65
26    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
26-->[:adreport] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\adreport\build\intermediates\library_manifest\release\AndroidManifest.xml:11:5-86
26-->[:adreport] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\adreport\build\intermediates\library_manifest\release\AndroidManifest.xml:11:22-83
27
28    <application
28-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:19:5-135:19
29        android:name="com.kaolafm.opensdk.demo.DemoApplication"
29-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:20:9-40
30        android:allowBackup="true"
30-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:21:9-35
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->[androidx.core:core:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\5d415039a9196d7dcbbfb2b7a48427a4\core-1.1.0\AndroidManifest.xml:24:18-86
32        android:configChanges="orientation|screenSize|locale|layoutDirection|keyboard|screenLayout"
32-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:22:9-100
33        android:extractNativeLibs="false"
34        android:icon="@mipmap/ic_launcher_yt"
34-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:23:9-46
35        android:label="@string/app_name"
35-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:24:9-41
36        android:networkSecurityConfig="@xml/network_security_config"
36-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:25:9-69
37        android:supportsRtl="true"
37-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:26:9-35
38        android:theme="@style/AppTheme" >
38-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:27:9-40
39        <activity
39-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:29:9-31:40
40            android:name="com.kaolafm.opensdk.demo.emergency.EmergencyActivity"
40-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:30:13-56
41            android:exported="false" />
41-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:31:13-37
42        <activity
42-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:32:9-34:40
43            android:name="com.kaolafm.opensdk.demo.recommed.RecommendActivity"
43-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:33:13-55
44            android:exported="false" />
44-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:34:13-37
45        <activity android:name="com.kaolafm.opensdk.demo.account.UserDurationActivity" />
45-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:35:9-66
45-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:35:19-63
46
47        <uses-library
47-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:37:9-39:40
48            android:name="org.apache.http.legacy"
48-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:38:13-50
49            android:required="false" />
49-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:39:13-37
50
51        <activity android:name="com.kaolafm.opensdk.demo.MainActivity" >
51-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:41:9-47:20
51-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:41:19-47
52            <intent-filter>
52-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:42:13-46:29
53                <action android:name="android.intent.action.MAIN" />
53-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:43:17-69
53-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:43:25-66
54
55                <category android:name="android.intent.category.LAUNCHER" />
55-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:45:17-77
55-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:45:27-74
56            </intent-filter>
57        </activity>
58        <activity
58-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:48:9-50:47
59            android:name="com.kaolafm.opensdk.demo.detail.DetailActivity"
59-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:49:13-50
60            android:label="@string/details" />
60-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:50:13-44
61        <activity android:name="com.kaolafm.opensdk.demo.operation.column.ColumnActivity" />
61-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:51:9-69
61-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:51:19-66
62        <activity android:name="com.kaolafm.opensdk.demo.operation.column.ColumnDetailActivity" />
62-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:52:9-75
62-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:52:19-72
63        <activity android:name="com.kaolafm.opensdk.demo.operation.category.CategoryActivity" />
63-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:53:9-73
63-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:53:19-70
64        <activity android:name="com.kaolafm.opensdk.demo.login.LoginActivity" />
64-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:54:9-57
64-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:54:19-54
65        <activity android:name="com.kaolafm.opensdk.demo.search.VoiceSearchActivity" />
65-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:55:9-64
65-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:55:19-61
66        <activity android:name="com.kaolafm.opensdk.demo.search.SearchResultActivity" />
66-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:56:9-65
66-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:56:19-62
67        <activity android:name="com.kaolafm.opensdk.demo.search.ProgramDetailActivity" />
67-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:57:9-66
67-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:57:19-63
68        <activity android:name="com.kaolafm.opensdk.demo.operation.category.CategoryInfoActivity" />
68-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:58:9-77
68-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:58:19-74
69        <activity android:name="com.kaolafm.opensdk.demo.operation.category.CategoryMemberActivity" />
69-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:59:9-79
69-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:59:19-76
70        <activity android:name="com.kaolafm.opensdk.demo.subcribe.SubscribeActivity" />
70-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:60:9-64
70-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:60:19-61
71        <activity android:name="com.kaolafm.opensdk.demo.login.KaolaLoginActivity" />
71-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:61:9-62
71-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:61:19-59
72        <activity android:name="com.kaolafm.opensdk.demo.login.QQMusicLoginActivity" />
72-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:62:9-64
72-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:62:19-61
73        <activity android:name="com.kaolafm.opensdk.demo.live.LiveActivity" />
73-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:63:9-55
73-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:63:19-52
74        <activity android:name="com.kaolafm.opensdk.demo.qqmusic.CollectionActivity" />
74-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:64:9-64
74-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:64:19-61
75        <activity android:name="com.kaolafm.opensdk.demo.qqmusic.CollectionListActivity" />
75-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:65:9-68
75-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:65:19-65
76        <activity android:name="com.kaolafm.opensdk.demo.account.LinkAccountActivity" />
76-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:66:9-65
76-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:66:19-62
77        <activity android:name="com.kaolafm.opensdk.demo.qqmusic.QQMusicActivity" />
77-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:67:9-61
77-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:67:19-58
78
79        <service android:name="com.kaolafm.opensdk.player.core.PlayerService" />
79-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:69:9-81
79-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:69:18-78
80
81        <meta-data
82            android:name="com.kaolafm.open.sdk.AppKey"
82-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:72:13-55
83            android:value="f6dff42133bf06810a52a1d392b9906b" />
83-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:73:13-39
84        <meta-data
85            android:name="com.kaolafm.open.sdk.AppId"
85-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:75:13-54
86            android:value="ye8192" />
86-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:76:13-38
87        <meta-data
88            android:name="com.kaolafm.open.sdk.Channel"
88-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:78:13-56
89            android:value="ceshizhuanyong_kradio" />
89-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:79:13-39
90        <meta-data
90-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:80:9-82:35
91            android:name="com.kaolafm.open.sdk.CarType"
91-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:81:13-56
92            android:value="xx5" />
92-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:82:13-32
93        <!--
94 <meta-data
95             android:name="com.kaolafm.open.sdk.freeContent"
96             android:value="true"
97             />
98        -->
99        <!-- <meta-data -->
100        <!-- android:name="com.kaolafm.ad.AppId" -->
101        <!-- android:value="8fbf02809128e437bb29383a6f8d0e2a" -->
102        <!-- /> -->
103        <!--
104             网易聊天室的 APP key, 可以在这里设置，也可以在 SDKOptions 中提供。
105            如果 SDKOptions 中提供了，取 SDKOptions 中的值。
106        -->
107        <meta-data
107-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:97:9-99:64
108            android:name="com.netease.nim.appKey"
108-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:98:13-50
109            android:value="34e5e3c8a9a3e3a29e72c145ab70b5b2" />
109-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:99:13-61
110        <meta-data
110-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:100:9-102:40
111            android:name="com.kaolafm.open.sdk.qqmusic.AppId"
111-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:101:13-62
112            android:value="12345700" />
112-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:102:13-37
113        <meta-data
113-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:103:9-105:50
114            android:name="com.kaolafm.open.sdk.qqmusic.AppKey"
114-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:104:13-63
115            android:value="UyZaTlMrnqSJKNLsoy" />
115-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:105:13-47
116
117        <activity android:name="com.kaolafm.opensdk.demo.brandinfo.BrandInfoActivity" />
117-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:107:9-65
117-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:107:19-62
118        <activity android:name="com.kaolafm.opensdk.demo.scene.SceneActivity" />
118-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:108:9-57
118-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:108:19-54
119        <activity android:name="com.kaolafm.opensdk.demo.player.RadioPlayerActivity" />
119-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:109:9-64
119-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:109:19-61
120        <activity android:name="com.kaolafm.opensdk.demo.player.BroadcastListPlayerActivity" />
120-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:110:9-72
120-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:110:19-69
121        <activity android:name="com.kaolafm.opensdk.demo.player.RadioPlayerGetListByAreaActivity" />
121-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:111:9-77
121-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:111:19-74
122        <activity android:name="com.kaolafm.opensdk.demo.player.BroadcastPlayerActivity" />
122-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:112:9-68
122-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:112:19-65
123        <activity android:name="com.kaolafm.opensdk.demo.player.BroadcastPlayerGetLocalByIdActivity" />
123-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:113:9-80
123-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:113:19-77
124        <activity android:name="com.kaolafm.opensdk.demo.player.AlbumPlayerActivity" />
124-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:114:9-64
124-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:114:19-61
125        <activity android:name="com.kaolafm.opensdk.demo.player.AudioPlayerActivity" />
125-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:115:9-64
125-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:115:19-61
126        <activity android:name="com.kaolafm.opensdk.demo.player.TVPlayerActivity" />
126-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:116:9-61
126-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:116:19-58
127        <activity android:name="com.kaolafm.opensdk.demo.player.QQMusicPlayerActivity" />
127-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:117:9-66
127-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:117:19-63
128        <activity android:name="com.kaolafm.opensdk.demo.history.HistoryActivity" />
128-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:118:9-61
128-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:118:19-58
129        <activity android:name="com.kaolafm.opensdk.demo.player.SubscribePlayerActivity" />
129-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:119:9-68
129-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:119:19-65
130        <activity android:name="com.kaolafm.opensdk.demo.search.KeywordSearchActivity" />
130-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:120:9-66
130-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:120:19-63
131        <activity android:name="com.kaolafm.opensdk.demo.personalise.InterestActivity" />
131-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:121:9-66
131-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:121:19-63
132        <activity android:name="com.kaolafm.opensdk.demo.personalise.UserActivity" />
132-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:122:9-62
132-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:122:19-59
133        <activity android:name="com.kaolafm.opensdk.demo.player.AudioDetailActivity" />
133-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:123:9-64
133-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:123:19-61
134        <activity android:name="com.kaolafm.opensdk.demo.DownloadActivity" />
134-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:124:9-54
134-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:124:19-51
135        <activity android:name="com.kaolafm.opensdk.demo.purchase.VipMealsActivity" />
135-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:125:9-63
135-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:125:19-60
136        <activity android:name="com.kaolafm.opensdk.demo.purchase.VipQRCodeActivity" />
136-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:126:9-64
136-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:126:19-61
137        <activity android:name="com.kaolafm.opensdk.demo.purchase.AlbumQRCodeActivity" />
137-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:127:9-66
137-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:127:19-63
138        <activity android:name="com.kaolafm.opensdk.demo.purchase.AudiosQRCodeActivity" />
138-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:128:9-67
138-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:128:19-64
139        <activity android:name="com.kaolafm.opensdk.demo.purchase.QRCodeStatusActivity" />
139-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:129:9-67
139-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:129:19-64
140        <activity android:name="com.kaolafm.opensdk.demo.purchase.BuyAlbumByCoinActivity" />
140-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:130:9-69
140-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:130:19-66
141        <activity android:name="com.kaolafm.opensdk.demo.purchase.BuyAudiosByCoinActivity" />
141-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:131:9-70
141-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:131:19-67
142        <activity android:name="com.kaolafm.opensdk.demo.purchase.PurchasedActivity" />
142-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:132:9-64
142-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:132:19-61
143        <activity android:name="com.kaolafm.opensdk.demo.purchase.OrderActivity" />
143-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:133:9-60
143-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:133:19-57
144        <activity android:name="com.kaolafm.opensdk.demo.activity.ActivitiesActivity" />
144-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:134:9-65
144-->C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\AndroidManifest.xml:134:19-62
145        <!-- 声明云信后台服务 -->
146        <service
146-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:29:9-31:39
147            android:name="com.netease.nimlib.service.NimService"
147-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:30:13-65
148            android:process=":core" /> <!-- 运行后台辅助服务 -->
148-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:31:13-36
149        <service
149-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:34:9-36:39
150            android:name="com.netease.nimlib.service.NimService$Aux"
150-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:35:13-69
151            android:process=":core" /> <!-- 声明云信后台辅助服务 -->
151-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:36:13-36
152        <service
152-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:39:9-43:39
153            android:name="com.netease.nimlib.job.NIMJobService"
153-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:40:13-64
154            android:exported="true"
154-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:41:13-36
155            android:permission="android.permission.BIND_JOB_SERVICE"
155-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:42:13-69
156            android:process=":core" /> <!-- 云信SDK的监视系统启动和网络变化的广播接收器，用户开机自启动以及网络变化时候重新登录 -->
156-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:43:13-36
157        <receiver
157-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:46:9-54:20
158            android:name="com.netease.nimlib.service.NimReceiver"
158-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:47:13-66
159            android:exported="false"
159-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:48:13-37
160            android:process=":core" >
160-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:49:13-36
161            <intent-filter>
161-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:50:13-53:29
162                <action android:name="android.intent.action.BOOT_COMPLETED" />
162-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:51:17-79
162-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:51:25-76
163                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
163-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:52:17-79
163-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:52:25-76
164            </intent-filter>
165        </receiver> <!-- 云信进程间通信receiver -->
166        <receiver android:name="com.netease.nimlib.service.ResponseReceiver" /> <!-- 云信进程间通信service -->
166-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:57:9-80
166-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:57:19-77
167        <service android:name="com.netease.nimlib.service.ResponseService" />
167-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:60:9-78
167-->[com.netease.nimlib:basesdk:5.1.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\78bf2d4b91085920f590d2d7c5277cef\jetified-basesdk-5.1.1\AndroidManifest.xml:60:18-75
168
169        <provider
169-->[androidx.lifecycle:lifecycle-process:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c088e46a6aa824f7af5801e67040d0c6\jetified-lifecycle-process-2.2.0\AndroidManifest.xml:25:9-29:43
170            android:name="androidx.lifecycle.ProcessLifecycleOwnerInitializer"
170-->[androidx.lifecycle:lifecycle-process:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c088e46a6aa824f7af5801e67040d0c6\jetified-lifecycle-process-2.2.0\AndroidManifest.xml:26:13-79
171            android:authorities="com.edog.car.ceshizhuanyong_kradio.lifecycle-process"
171-->[androidx.lifecycle:lifecycle-process:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c088e46a6aa824f7af5801e67040d0c6\jetified-lifecycle-process-2.2.0\AndroidManifest.xml:27:13-69
172            android:exported="false"
172-->[androidx.lifecycle:lifecycle-process:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c088e46a6aa824f7af5801e67040d0c6\jetified-lifecycle-process-2.2.0\AndroidManifest.xml:28:13-37
173            android:multiprocess="true" />
173-->[androidx.lifecycle:lifecycle-process:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\c088e46a6aa824f7af5801e67040d0c6\jetified-lifecycle-process-2.2.0\AndroidManifest.xml:29:13-40
174
175        <service
175-->[:ad] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\ad\build\intermediates\library_manifest\release\AndroidManifest.xml:16:9-18:72
176            android:name="com.kaolafm.ad.timer.TimerJobService"
176-->[:ad] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\ad\build\intermediates\library_manifest\release\AndroidManifest.xml:17:13-64
177            android:permission="android.permission.BIND_JOB_SERVICE" />
177-->[:ad] C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\ad\build\intermediates\library_manifest\release\AndroidManifest.xml:18:13-69
178        <service
178-->[androidx.room:room-runtime:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\958824cfaac20f2d3074778062c67621\room-runtime-2.4.1\AndroidManifest.xml:25:9-28:40
179            android:name="androidx.room.MultiInstanceInvalidationService"
179-->[androidx.room:room-runtime:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\958824cfaac20f2d3074778062c67621\room-runtime-2.4.1\AndroidManifest.xml:26:13-74
180            android:directBootAware="true"
180-->[androidx.room:room-runtime:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\958824cfaac20f2d3074778062c67621\room-runtime-2.4.1\AndroidManifest.xml:27:13-43
181            android:exported="false" />
181-->[androidx.room:room-runtime:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-2\files-2.1\958824cfaac20f2d3074778062c67621\room-runtime-2.4.1\AndroidManifest.xml:28:13-37
182    </application>
183
184</manifest>
