<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="urn:oasis:names:tc:xliff:document:1.2">
    <string-array name="account">
        <item>5.1 云听账号登录</item>
        <item>5.2 云听账号收听时长</item>
    </string-array>
    <string-array name="activity">
        <item>8.1 活动列表</item>
    </string-array>
    <string-array name="child_index">
        <item>@array/prepare</item>
        <item>@array/operation</item>
        <item>@array/content</item>
        <item>@array/search</item>
        <item>@array/account</item>
        <item>@array/user</item>
        <item>@array/purchase</item>
        <item>@array/activity</item>
        <item>@array/other</item>
    </string-array>
    <string-array name="content">
        <item>3.1 专辑</item>
        <item>3.2 单曲</item>
        <item>3.3 AI电台</item>
        <item>3.4 AI电台(地区)</item>
        <item>3.5 在线广播(批量查询)</item>
        <item>3.6 在线广播</item>
        <item>3.7 在线广播(本地或国家台)</item>
        <item>3.8 报时</item>
        <item>3.9 听电视</item>
        <item>3.10 场景推荐</item>
    </string-array>
    <string-array name="dialog_cp_name">
        <item>考拉</item>
        <item>QQ音乐</item>
    </string-array>
    <string-array name="dialog_play_or_show">
        <item>显示歌曲列表</item>
        <item>播放歌单</item>
    </string-array>
    <string-array name="main_list">
        <item>1.准备阶段</item>
        <item>2.运营相关</item>
        <item>3.内容相关</item>
        <item>4.搜索相关</item>
        <item>5.账号相关</item>
        <item>6.用户相关</item>
        <item>7.付费</item>
        <item>8.活动</item>
        <item>9.其他</item>
    </string-array>
    <string-array name="operation">
        <item>2.1 栏目</item>
        <item>2.2 分类</item>
    </string-array>
    <string-array name="other">
        <item>9.1 应急广播</item>
    </string-array>
    <string-array name="prepare">
        <item>1.1 初始化SDK</item>
        <item>1.2 设备激活</item>
        <item>1.3 初始化并激活</item>
        <item>1.4 获取激活状态</item>
        <item>1.5 获取设备ID</item>
        <item>1.6 品牌信息</item>
        <item>1.7 设置位置信息</item>
        <item>1.8 获取位置信息</item>
        <item>1.9 获取唯一标志</item>
        <item>2.0 切换环境</item>
    </string-array>
    <string-array name="purchase">
        <item>7.1 vip套餐</item>
        <item>7.2 vip二维码</item>
        <item>7.3 专辑二维码</item>
        <item>7.4 单曲二维码</item>
        <item>7.5 查看二维码状态</item>
        <item>7.6 已购列表</item>
        <item>7.7 订单列表</item>
    </string-array>
    <string-array name="qqmusic_child_index">
        <item>@array/qqmusic_mine</item>
        <item>@array/qqmusic_radio</item>
    </string-array>
    <string-array name="qqmusic_functions">
        <item>功能</item>
        <item>电台</item>
    </string-array>
    <string-array name="qqmusic_mine">
        <item>私人FM</item>
        <item>我喜欢的</item>
        <item>每日30首</item>
        <item>我的收藏</item>
    </string-array>
    <string-array name="qqmusic_radio">
        <item>场景电台</item>
        <item>标签电台</item>
        <item>排行榜</item>
    </string-array>
    <integer-array name="res_type">
        <item>10</item>
        <item>11</item>
        <item>12</item>
        <item>13</item>
        <item>14</item>
        <item>15</item>
        <item>16</item>
        <item>17</item>
        <item>18</item>
        <item>19</item>
        <item>20</item>
        <item>21</item>
        <item>22</item>
        <item>23</item>
        <item>24</item>
        <item>25</item>
        <item>27</item>
        <item>28</item>
        <item>29</item>
        <item>31</item>
        <item>32</item>
        <item>33</item>
        <item>34</item>
        <item>35</item>
        <item>36</item>
        <item>37</item>
        <item>38</item>
        <item>39</item>
    </integer-array>
    <string-array name="search">
        <item>4.1 语义搜索</item>
        <item>4.2 关键词搜索</item>
    </string-array>
    <string-array name="search_type_list">
        <item>综合</item>
        <item>AI电台</item>
        <item>专辑</item>
        <item>单曲</item>
        <item>在线广播</item>
        <item>听电视</item>
    </string-array>
    <string-array name="search_voice_source">
        <item>考拉</item>
        <item>同行者</item>
        <item>出门问问</item>
        <item>思必驰</item>
        <item>蓦然</item>
        <item>科大讯飞</item>
    </string-array>
    <string-array name="sound_quality">
        <item>低品质</item>
        <item>标准品质</item>
        <item>高品质</item>
        <item>较高品质</item>
    </string-array>
    <string-array name="user">
        <item>6.1 订阅</item>
        <item>6.2 收听历史</item>
    </string-array>
    <attr format="boolean" name="barrierAllowsGoneWidgets"/>
    <attr format="enum" name="barrierDirection">
        <enum name="left" value="0"/>
        <enum name="right" value="1"/>
        <enum name="top" value="2"/>
        <enum name="bottom" value="3"/>
        <enum name="start" value="5"/>
        <enum name="end" value="6"/>
    </attr>
    <attr format="boolean" name="chainUseRtl"/>
    <attr format="reference" name="constraintSet"/>
    <attr format="string" name="constraint_referenced_ids"/>
    <attr format="reference" name="content"/>
    <attr format="reference" name="coordinatorLayoutStyle"/>
    <attr format="reference" name="drawerArrowStyle"/>
    <attr name="emptyVisibility">
        <enum name="gone" value="0"/>
        <enum name="invisible" value="1"/>
    </attr>
    <attr format="dimension" name="height"/>
    <attr format="boolean" name="isLightTheme"/>
    <attr format="boolean" name="layout_constrainedHeight"/>
    <attr format="boolean" name="layout_constrainedWidth"/>
    <attr format="integer" name="layout_constraintBaseline_creator"/>
    <attr format="reference|enum" name="layout_constraintBaseline_toBaselineOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="integer" name="layout_constraintBottom_creator"/>
    <attr format="reference|enum" name="layout_constraintBottom_toBottomOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintBottom_toTopOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference" name="layout_constraintCircle"/>
    <attr format="integer" name="layout_constraintCircleAngle"/>
    <attr format="dimension" name="layout_constraintCircleRadius"/>
    <attr format="string" name="layout_constraintDimensionRatio"/>
    <attr format="reference|enum" name="layout_constraintEnd_toEndOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintEnd_toStartOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="dimension" name="layout_constraintGuide_begin"/>
    <attr format="dimension" name="layout_constraintGuide_end"/>
    <attr format="float" name="layout_constraintGuide_percent"/>
    <attr name="layout_constraintHeight_default">
        <enum name="spread" value="0"/>
        <enum name="wrap" value="1"/>
        <enum name="percent" value="2"/>
    </attr>
    <attr format="dimension|enum" name="layout_constraintHeight_max">
        <enum name="wrap" value="-2"/>
    </attr>
    <attr format="dimension|enum" name="layout_constraintHeight_min">
        <enum name="wrap" value="-2"/>
    </attr>
    <attr format="float" name="layout_constraintHeight_percent"/>
    <attr format="float" name="layout_constraintHorizontal_bias"/>
    <attr format="enum" name="layout_constraintHorizontal_chainStyle">
        <enum name="spread" value="0"/>
        <enum name="spread_inside" value="1"/>
        <enum name="packed" value="2"/>
    </attr>
    <attr format="float" name="layout_constraintHorizontal_weight"/>
    <attr format="integer" name="layout_constraintLeft_creator"/>
    <attr format="reference|enum" name="layout_constraintLeft_toLeftOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintLeft_toRightOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="integer" name="layout_constraintRight_creator"/>
    <attr format="reference|enum" name="layout_constraintRight_toLeftOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintRight_toRightOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintStart_toEndOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintStart_toStartOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="integer" name="layout_constraintTop_creator"/>
    <attr format="reference|enum" name="layout_constraintTop_toBottomOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintTop_toTopOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="float" name="layout_constraintVertical_bias"/>
    <attr format="enum" name="layout_constraintVertical_chainStyle">
        <enum name="spread" value="0"/>
        <enum name="spread_inside" value="1"/>
        <enum name="packed" value="2"/>
    </attr>
    <attr format="float" name="layout_constraintVertical_weight"/>
    <attr name="layout_constraintWidth_default">
        <enum name="spread" value="0"/>
        <enum name="wrap" value="1"/>
        <enum name="percent" value="2"/>
    </attr>
    <attr format="dimension|enum" name="layout_constraintWidth_max">
        <enum name="wrap" value="-2"/>
    </attr>
    <attr format="dimension|enum" name="layout_constraintWidth_min">
        <enum name="wrap" value="-2"/>
    </attr>
    <attr format="float" name="layout_constraintWidth_percent"/>
    <attr format="dimension" name="layout_editor_absoluteX"/>
    <attr format="dimension" name="layout_editor_absoluteY"/>
    <attr format="dimension" name="layout_goneMarginBottom"/>
    <attr format="dimension" name="layout_goneMarginEnd"/>
    <attr format="dimension" name="layout_goneMarginLeft"/>
    <attr format="dimension" name="layout_goneMarginRight"/>
    <attr format="dimension" name="layout_goneMarginStart"/>
    <attr format="dimension" name="layout_goneMarginTop"/>
    <attr name="layout_optimizationLevel">
        <flag name="none" value="0"/>
        <flag name="standard" value="7"/> <!-- direct, barriers, chains -->
        <flag name="direct" value="1"/>
        <flag name="barrier" value="2"/>
        <flag name="chains" value="4"/>
        <flag name="dimensions" value="8"/>
        <flag name="groups" value="32"/>
    </attr>
    <attr format="string" name="title"/>
    <bool name="abc_action_bar_embed_tabs">true</bool>
    <bool name="abc_allow_stacked_button_bar">false</bool>
    <bool name="abc_config_actionMenuItemAllCaps">true</bool>
    <color name="Blue">#4674e7</color>
    <color name="Green">#0ba62c</color>
    <color name="Orange">#e75946</color>
    <color name="Yellow">#f4d227</color>
    <color name="abc_input_method_navigation_guard">@android:color/black</color>
    <color name="abc_search_url_text_normal">#7fa87f</color>
    <color name="abc_search_url_text_pressed">@android:color/black</color>
    <color name="abc_search_url_text_selected">@android:color/black</color>
    <color name="accent_material_dark">@color/material_deep_teal_200</color>
    <color name="accent_material_light">@color/material_deep_teal_500</color>
    <color name="background_floating_material_dark">@color/material_grey_800</color>
    <color name="background_floating_material_light">@android:color/white</color>
    <color name="background_material_dark">@color/material_grey_850</color>
    <color name="background_material_light">@color/material_grey_50</color>
    <color name="bright_foreground_disabled_material_dark">#80ffffff</color>
    <color name="bright_foreground_disabled_material_light">#80000000</color>
    <color name="bright_foreground_inverse_material_dark">@color/bright_foreground_material_light</color>
    <color name="bright_foreground_inverse_material_light">@color/bright_foreground_material_dark</color>
    <color name="bright_foreground_material_dark">@android:color/white</color>
    <color name="bright_foreground_material_light">@android:color/black</color>
    <color name="button_material_dark">#ff5a595b</color>
    <color name="button_material_light">#ffd6d7d7</color>
    <color name="colorAccent">#FF4081</color>
    <color name="colorBlack">#000000</color>
    <color name="colorMainButtonClick">#108BAC</color>
    <color name="colorPlayerLayerFirstCenter">#131834</color>
    <color name="colorPlayerLayerFirstEnd">#1F2647</color>
    <color name="colorPlayerLayerFirstStart">#1F284B</color>
    <color name="colorPlayerLayerSecondCenter">@color/colorPlayerLayerFirstCenter</color>
    <color name="colorPlayerLayerSecondTopBottom">#00131834</color>
    <color name="colorPrimary">#3F51B5</color>
    <color name="colorPrimaryDark">#303F9F</color>
    <color name="colorTransparent">#00000000</color>
    <color name="color_black_30_transparent">#40000000</color>
    <color name="color_black_50_transparent">#80000000</color>
    <color name="colorhahaha">#9C27B0</color>
    <color name="dim_foreground_disabled_material_dark">#80bebebe</color>
    <color name="dim_foreground_disabled_material_light">#80323232</color>
    <color name="dim_foreground_material_dark">#ffbebebe</color>
    <color name="dim_foreground_material_light">#ff323232</color>
    <color name="error_color_material_dark">#ff7043</color>
    <color name="error_color_material_light">#ff5722</color>
    <color name="foreground_material_dark">@android:color/white</color>
    <color name="foreground_material_light">@android:color/black</color>
    <color name="highlighted_text_material_dark">#6680cbc4</color>
    <color name="highlighted_text_material_light">#66009688</color>
    <color name="material_blue_grey_800">#ff37474f</color>
    <color name="material_blue_grey_900">#ff263238</color>
    <color name="material_blue_grey_950">#ff21272b</color>
    <color name="material_deep_teal_200">#ff80cbc4</color>
    <color name="material_deep_teal_500">#ff008577</color>
    <color name="material_grey_100">#fff5f5f5</color>
    <color name="material_grey_300">#ffe0e0e0</color>
    <color name="material_grey_50">#fffafafa</color>
    <color name="material_grey_600">#ff757575</color>
    <color name="material_grey_800">#ff424242</color>
    <color name="material_grey_850">#ff303030</color>
    <color name="material_grey_900">#ff212121</color>
    <color name="notification_action_color_filter">#ffffffff</color>
    <color name="notification_icon_bg_color">#ff9e9e9e</color>
    <color name="notification_material_background_media_default_color">#ff424242</color>
    <color name="primary_dark_material_dark">@android:color/black</color>
    <color name="primary_dark_material_light">@color/material_grey_600</color>
    <color name="primary_material_dark">@color/material_grey_900</color>
    <color name="primary_material_light">@color/material_grey_100</color>
    <color name="primary_text_default_material_dark">#ffffffff</color>
    <color name="primary_text_default_material_light">#de000000</color>
    <color name="primary_text_disabled_material_dark">#4Dffffff</color>
    <color name="primary_text_disabled_material_light">#39000000</color>
    <color name="ripple_material_dark">#33ffffff</color>
    <color name="ripple_material_light">#1f000000</color>
    <color name="secondary_text_default_material_dark">#b3ffffff</color>
    <color name="secondary_text_default_material_light">#8a000000</color>
    <color name="secondary_text_disabled_material_dark">#36ffffff</color>
    <color name="secondary_text_disabled_material_light">#24000000</color>
    <color name="switch_thumb_disabled_material_dark">#ff616161</color>
    <color name="switch_thumb_disabled_material_light">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_dark">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_light">#fff1f1f1</color>
    <color name="text_color_1">#282828</color>
    <color name="text_color_2">#5A5A5A</color>
    <color name="text_color_3">#282828</color>
    <color name="tooltip_background_dark">#e6616161</color>
    <color name="tooltip_background_light">#e6FFFFFF</color>
    <dimen name="abc_action_bar_content_inset_material">16dp</dimen>
    <dimen name="abc_action_bar_content_inset_with_nav">72dp</dimen>
    <dimen name="abc_action_bar_default_height_material">56dp</dimen>
    <dimen name="abc_action_bar_default_padding_end_material">0dp</dimen>
    <dimen name="abc_action_bar_default_padding_start_material">0dp</dimen>
    <dimen name="abc_action_bar_elevation_material">4dp</dimen>
    <dimen name="abc_action_bar_icon_vertical_padding_material">16dp</dimen>
    <dimen name="abc_action_bar_overflow_padding_end_material">10dp</dimen>
    <dimen name="abc_action_bar_overflow_padding_start_material">6dp</dimen>
    <dimen name="abc_action_bar_stacked_max_height">48dp</dimen>
    <dimen name="abc_action_bar_stacked_tab_max_width">180dp</dimen>
    <dimen name="abc_action_bar_subtitle_bottom_margin_material">5dp</dimen>
    <dimen name="abc_action_bar_subtitle_top_margin_material">-3dp</dimen>
    <dimen name="abc_action_button_min_height_material">48dp</dimen>
    <dimen name="abc_action_button_min_width_material">48dp</dimen>
    <dimen name="abc_action_button_min_width_overflow_material">36dp</dimen>
    <dimen name="abc_alert_dialog_button_bar_height">48dp</dimen>
    <dimen name="abc_alert_dialog_button_dimen">48dp</dimen>
    <dimen name="abc_button_inset_horizontal_material">@dimen/abc_control_inset_material</dimen>
    <dimen name="abc_button_inset_vertical_material">6dp</dimen>
    <dimen name="abc_button_padding_horizontal_material">8dp</dimen>
    <dimen name="abc_button_padding_vertical_material">@dimen/abc_control_padding_material</dimen>
    <dimen name="abc_cascading_menus_min_smallest_width">720dp</dimen>
    <dimen name="abc_config_prefDialogWidth">320dp</dimen>
    <dimen name="abc_control_corner_material">2dp</dimen>
    <dimen name="abc_control_inset_material">4dp</dimen>
    <dimen name="abc_control_padding_material">4dp</dimen>
    <dimen name="abc_dialog_corner_radius_material">2dp</dimen>
    <item name="abc_dialog_fixed_height_major" type="dimen">80%</item>
    <item name="abc_dialog_fixed_height_minor" type="dimen">100%</item>
    <item name="abc_dialog_fixed_width_major" type="dimen">320dp</item>
    <item name="abc_dialog_fixed_width_minor" type="dimen">320dp</item>
    <dimen name="abc_dialog_list_padding_bottom_no_buttons">8dp</dimen>
    <dimen name="abc_dialog_list_padding_top_no_title">8dp</dimen>
    <item name="abc_dialog_min_width_major" type="dimen">65%</item>
    <item name="abc_dialog_min_width_minor" type="dimen">95%</item>
    <dimen name="abc_dialog_padding_material">24dp</dimen>
    <dimen name="abc_dialog_padding_top_material">18dp</dimen>
    <dimen name="abc_dialog_title_divider_material">8dp</dimen>
    <item format="float" name="abc_disabled_alpha_material_dark" type="dimen">0.30</item>
    <item format="float" name="abc_disabled_alpha_material_light" type="dimen">0.26</item>
    <dimen name="abc_dropdownitem_icon_width">32dip</dimen>
    <dimen name="abc_dropdownitem_text_padding_left">8dip</dimen>
    <dimen name="abc_dropdownitem_text_padding_right">8dip</dimen>
    <dimen name="abc_edit_text_inset_bottom_material">7dp</dimen>
    <dimen name="abc_edit_text_inset_horizontal_material">4dp</dimen>
    <dimen name="abc_edit_text_inset_top_material">10dp</dimen>
    <dimen name="abc_floating_window_z">16dp</dimen>
    <dimen name="abc_list_item_height_large_material">80dp</dimen>
    <dimen name="abc_list_item_height_material">64dp</dimen>
    <dimen name="abc_list_item_height_small_material">48dp</dimen>
    <dimen name="abc_list_item_padding_horizontal_material">@dimen/abc_action_bar_content_inset_material</dimen>
    <dimen name="abc_panel_menu_list_width">296dp</dimen>
    <dimen name="abc_progress_bar_height_material">4dp</dimen>
    <dimen name="abc_search_view_preferred_height">48dip</dimen>
    <dimen name="abc_search_view_preferred_width">320dip</dimen>
    <dimen name="abc_seekbar_track_background_height_material">2dp</dimen>
    <dimen name="abc_seekbar_track_progress_height_material">2dp</dimen>
    <dimen name="abc_select_dialog_padding_start_material">20dp</dimen>
    <dimen name="abc_switch_padding">3dp</dimen>
    <dimen name="abc_text_size_body_1_material">14sp</dimen>
    <dimen name="abc_text_size_body_2_material">14sp</dimen>
    <dimen name="abc_text_size_button_material">14sp</dimen>
    <dimen name="abc_text_size_caption_material">12sp</dimen>
    <dimen name="abc_text_size_display_1_material">34sp</dimen>
    <dimen name="abc_text_size_display_2_material">45sp</dimen>
    <dimen name="abc_text_size_display_3_material">56sp</dimen>
    <dimen name="abc_text_size_display_4_material">112sp</dimen>
    <dimen name="abc_text_size_headline_material">24sp</dimen>
    <dimen name="abc_text_size_large_material">22sp</dimen>
    <dimen name="abc_text_size_medium_material">18sp</dimen>
    <dimen name="abc_text_size_menu_header_material">14sp</dimen>
    <dimen name="abc_text_size_menu_material">16sp</dimen>
    <dimen name="abc_text_size_small_material">14sp</dimen>
    <dimen name="abc_text_size_subhead_material">16sp</dimen>
    <dimen name="abc_text_size_subtitle_material_toolbar">16dp</dimen>
    <dimen name="abc_text_size_title_material">20sp</dimen>
    <dimen name="abc_text_size_title_material_toolbar">20dp</dimen>
    <dimen name="compat_button_inset_horizontal_material">4dp</dimen>
    <dimen name="compat_button_inset_vertical_material">6dp</dimen>
    <dimen name="compat_button_padding_horizontal_material">8dp</dimen>
    <dimen name="compat_button_padding_vertical_material">4dp</dimen>
    <dimen name="compat_control_corner_material">2dp</dimen>
    <dimen name="compat_notification_large_icon_max_height">320dp</dimen>
    <dimen name="compat_notification_large_icon_max_width">320dp</dimen>
    <item format="float" name="disabled_alpha_material_dark" type="dimen">0.30</item>
    <item format="float" name="disabled_alpha_material_light" type="dimen">0.26</item>
    <dimen name="fastscroll_default_thickness">8dp</dimen>
    <dimen name="fastscroll_margin">0dp</dimen>
    <dimen name="fastscroll_minimum_range">50dp</dimen>
    <item format="float" name="highlight_alpha_material_colored" type="dimen">0.26</item>
    <item format="float" name="highlight_alpha_material_dark" type="dimen">0.20</item>
    <item format="float" name="highlight_alpha_material_light" type="dimen">0.12</item>
    <item format="float" name="hint_alpha_material_dark" type="dimen">0.50</item>
    <item format="float" name="hint_alpha_material_light" type="dimen">0.38</item>
    <item format="float" name="hint_pressed_alpha_material_dark" type="dimen">0.70</item>
    <item format="float" name="hint_pressed_alpha_material_light" type="dimen">0.54</item>
    <dimen name="item_touch_helper_max_drag_scroll_per_frame">20dp</dimen>
    <dimen name="item_touch_helper_swipe_escape_max_velocity">800dp</dimen>
    <dimen name="item_touch_helper_swipe_escape_velocity">120dp</dimen>
    <dimen name="notification_action_icon_size">32dp</dimen>
    <dimen name="notification_action_text_size">13sp</dimen>
    <dimen name="notification_big_circle_margin">12dp</dimen>
    <dimen name="notification_content_margin_start">8dp</dimen>
    <dimen name="notification_large_icon_height">64dp</dimen>
    <dimen name="notification_large_icon_width">64dp</dimen>
    <dimen name="notification_main_column_padding_top">10dp</dimen>
    <dimen name="notification_media_narrow_margin">@dimen/notification_content_margin_start</dimen>
    <dimen name="notification_right_icon_size">16dp</dimen>
    <dimen name="notification_right_side_padding_top">2dp</dimen>
    <dimen name="notification_small_icon_background_padding">3dp</dimen>
    <dimen name="notification_small_icon_size_as_large">24dp</dimen>
    <dimen name="notification_subtext_size">13sp</dimen>
    <dimen name="notification_top_pad">10dp</dimen>
    <dimen name="notification_top_pad_large_text">5dp</dimen>
    <dimen name="subtitle_corner_radius">2dp</dimen>
    <dimen name="subtitle_outline_width">2dp</dimen>
    <dimen name="subtitle_shadow_offset">2dp</dimen>
    <dimen name="subtitle_shadow_radius">2dp</dimen>
    <dimen name="tooltip_corner_radius">2dp</dimen>
    <dimen name="tooltip_horizontal_padding">16dp</dimen>
    <dimen name="tooltip_margin">8dp</dimen>
    <dimen name="tooltip_precise_anchor_extra_offset">8dp</dimen>
    <dimen name="tooltip_precise_anchor_threshold">96dp</dimen>
    <dimen name="tooltip_vertical_padding">6.5dp</dimen>
    <dimen name="tooltip_y_offset_non_touch">0dp</dimen>
    <dimen name="tooltip_y_offset_touch">16dp</dimen>
    <drawable name="notification_template_icon_bg">#3333B5E5</drawable>
    <drawable name="notification_template_icon_low_bg">#0cffffff</drawable>
    <item name="accessibility_action_clickable_span" type="id"/>
    <item name="accessibility_custom_action_0" type="id"/>
    <item name="accessibility_custom_action_1" type="id"/>
    <item name="accessibility_custom_action_10" type="id"/>
    <item name="accessibility_custom_action_11" type="id"/>
    <item name="accessibility_custom_action_12" type="id"/>
    <item name="accessibility_custom_action_13" type="id"/>
    <item name="accessibility_custom_action_14" type="id"/>
    <item name="accessibility_custom_action_15" type="id"/>
    <item name="accessibility_custom_action_16" type="id"/>
    <item name="accessibility_custom_action_17" type="id"/>
    <item name="accessibility_custom_action_18" type="id"/>
    <item name="accessibility_custom_action_19" type="id"/>
    <item name="accessibility_custom_action_2" type="id"/>
    <item name="accessibility_custom_action_20" type="id"/>
    <item name="accessibility_custom_action_21" type="id"/>
    <item name="accessibility_custom_action_22" type="id"/>
    <item name="accessibility_custom_action_23" type="id"/>
    <item name="accessibility_custom_action_24" type="id"/>
    <item name="accessibility_custom_action_25" type="id"/>
    <item name="accessibility_custom_action_26" type="id"/>
    <item name="accessibility_custom_action_27" type="id"/>
    <item name="accessibility_custom_action_28" type="id"/>
    <item name="accessibility_custom_action_29" type="id"/>
    <item name="accessibility_custom_action_3" type="id"/>
    <item name="accessibility_custom_action_30" type="id"/>
    <item name="accessibility_custom_action_31" type="id"/>
    <item name="accessibility_custom_action_4" type="id"/>
    <item name="accessibility_custom_action_5" type="id"/>
    <item name="accessibility_custom_action_6" type="id"/>
    <item name="accessibility_custom_action_7" type="id"/>
    <item name="accessibility_custom_action_8" type="id"/>
    <item name="accessibility_custom_action_9" type="id"/>
    <item name="action_bar_activity_content" type="id"/>
    <item name="action_bar_spinner" type="id"/>
    <item name="action_menu_divider" type="id"/>
    <item name="action_menu_presenter" type="id"/>
    <item name="btn_switch_http" type="id"/>
    <item name="ex_header" type="id"/>
    <item name="fragment_container_view_tag" type="id"/>
    <item name="glide_custom_view_target_tag" type="id"/>
    <item name="home" type="id"/>
    <item name="item_touch_helper_previous_elevation" type="id"/>
    <item name="line1" type="id"/>
    <item name="line3" type="id"/>
    <item name="progress_circular" type="id"/>
    <item name="progress_horizontal" type="id"/>
    <item name="split_action_bar" type="id"/>
    <item name="tag_accessibility_actions" type="id"/>
    <item name="tag_accessibility_clickable_spans" type="id"/>
    <item name="tag_accessibility_heading" type="id"/>
    <item name="tag_accessibility_pane_title" type="id"/>
    <item name="tag_screen_reader_focusable" type="id"/>
    <item name="tag_transition_group" type="id"/>
    <item name="tag_unhandled_key_event_manager" type="id"/>
    <item name="tag_unhandled_key_listeners" type="id"/>
    <item name="text" type="id"/>
    <item name="text2" type="id"/>
    <item name="title" type="id"/>
    <item name="up" type="id"/>
    <id name="view_tree_lifecycle_owner"/>
    <item name="visible_removing_fragment_view_tag" type="id"/>
    <integer name="abc_config_activityDefaultDur">220</integer>
    <integer name="abc_config_activityShortDur">150</integer>
    <integer name="cancel_button_image_alpha">127</integer>
    <integer name="config_tooltipAnimTime">150</integer>
    <integer name="status_bar_notification_info_maxnum">999</integer>
    <string name="abc_action_bar_home_description">Navigate home</string>
    <string name="abc_action_bar_up_description">Navigate up</string>
    <string name="abc_action_menu_overflow_description">More options</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_activity_chooser_view_see_all">See all</string>
    <string name="abc_activitychooserview_choose_application">Choose an app</string>
    <string name="abc_capital_off">OFF</string>
    <string name="abc_capital_on">ON</string>
    <string name="abc_menu_alt_shortcut_label">Alt+</string>
    <string name="abc_menu_ctrl_shortcut_label">Ctrl+</string>
    <string name="abc_menu_delete_shortcut_label">delete</string>
    <string name="abc_menu_enter_shortcut_label">enter</string>
    <string name="abc_menu_function_shortcut_label">Function+</string>
    <string name="abc_menu_meta_shortcut_label">Meta+</string>
    <string name="abc_menu_shift_shortcut_label">Shift+</string>
    <string name="abc_menu_space_shortcut_label">space</string>
    <string name="abc_menu_sym_shortcut_label">Sym+</string>
    <string name="abc_prepend_shortcut_label">Menu+</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_query">Search query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_shareactionprovider_share_with">Share with</string>
    <string name="abc_shareactionprovider_share_with_application">Share with <ns1:g example="Mail" id="application_name">%s</ns1:g></string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="app_name" translatable="false">SdkDemo测试专用</string>
    <string name="details">详情</string>
    <string name="live_cancel_message">取消</string>
    <string name="live_click_to_finish">轻点完成</string>
    <string name="live_click_to_speak">点击说话</string>
    <string name="live_close">关闭</string>
    <string name="live_coming">主播还在墨迹中，再等等吧</string>
    <string name="live_default_message">我发了语音留言</string>
    <string name="live_file_not_exist">文件不存在</string>
    <string name="live_finished">直播已结束</string>
    <string name="live_go_listen_other">去听听其它的吧</string>
    <string name="live_hour_minute_second">%1$s小时%2$s分%3$s秒</string>
    <string name="live_leave_message_after_login">登录后语音留言</string>
    <string name="live_listen_message">试听</string>
    <string name="live_listening_number">%d 人正在收听</string>
    <string name="live_load_error">哎呀，信号不好。努力联网中……</string>
    <string name="live_member_enter">%s进入直播间</string>
    <string name="live_message_received">%s发送了一条语音留言</string>
    <string name="live_nickname_me">我</string>
    <string name="live_player">主播: %s</string>
    <string name="live_second">s</string>
    <string name="live_send_message">发送</string>
    <string name="live_speak_too_short">说话时间太短</string>
    <string name="live_start_interval">距离直播还有</string>
    <string name="live_upload_again">重新发送</string>
    <string name="live_upload_failed">发送失败</string>
    <string name="live_uploading">发送中</string>
    <string name="search_menu_title">Search</string>
    <string name="send">发送</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="type">类型</string>
    <style name="AlertDialog.AppCompat" parent="Base.AlertDialog.AppCompat"/>
    <style name="AlertDialog.AppCompat.Light" parent="Base.AlertDialog.AppCompat.Light"/>
    <style name="Animation.AppCompat.Dialog" parent="Base.Animation.AppCompat.Dialog"/>
    <style name="Animation.AppCompat.DropDownUp" parent="Base.Animation.AppCompat.DropDownUp"/>
    <style name="Animation.AppCompat.Tooltip" parent="Base.Animation.AppCompat.Tooltip"/>
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>
    <style name="Base.AlertDialog.AppCompat" parent="android:Widget">
        <item name="android:layout">@layout/abc_alert_dialog_material</item>
        <item name="listLayout">@layout/abc_select_dialog_material</item>
        <item name="listItemLayout">@layout/select_dialog_item_material</item>
        <item name="multiChoiceItemLayout">@layout/select_dialog_multichoice_material</item>
        <item name="singleChoiceItemLayout">@layout/select_dialog_singlechoice_material</item>
        <item name="buttonIconDimen">@dimen/abc_alert_dialog_button_dimen</item>
    </style>
    <style name="Base.AlertDialog.AppCompat.Light" parent="Base.AlertDialog.AppCompat"/>
    <style name="Base.Animation.AppCompat.Dialog" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/abc_popup_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_popup_exit</item>
    </style>
    <style name="Base.Animation.AppCompat.DropDownUp" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/abc_grow_fade_in_from_bottom</item>
        <item name="android:windowExitAnimation">@anim/abc_shrink_fade_out_from_bottom</item>
    </style>
    <style name="Base.Animation.AppCompat.Tooltip" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/abc_tooltip_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_tooltip_exit</item>
    </style>
    <style name="Base.DialogWindowTitle.AppCompat" parent="android:Widget">
        <item name="android:maxLines">1</item>
        <item name="android:scrollHorizontally">true</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Title</item>
    </style>
    <style name="Base.DialogWindowTitleBackground.AppCompat" parent="android:Widget">
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">?attr/dialogPreferredPadding</item>
        <item name="android:paddingRight">?attr/dialogPreferredPadding</item>
        <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat" parent="android:TextAppearance">
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHint">?android:textColorHint</item>
        <item name="android:textColorHighlight">?android:textColorHighlight</item>
        <item name="android:textColorLink">?android:textColorLink</item>
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Body1">
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Body2">
        <item name="android:textSize">@dimen/abc_text_size_body_2_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Button">
        <item name="android:textSize">@dimen/abc_text_size_button_material</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Caption">
        <item name="android:textSize">@dimen/abc_text_size_caption_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display1">
        <item name="android:textSize">@dimen/abc_text_size_display_1_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display2">
        <item name="android:textSize">@dimen/abc_text_size_display_2_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display3">
        <item name="android:textSize">@dimen/abc_text_size_display_3_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display4">
        <item name="android:textSize">@dimen/abc_text_size_display_4_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Headline">
        <item name="android:textSize">@dimen/abc_text_size_headline_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Large">
        <item name="android:textSize">@dimen/abc_text_size_large_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Large.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium">
        <item name="android:textSize">@dimen/abc_text_size_medium_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium.Inverse">
        <item name="android:textColor">?android:attr/textColorSecondaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Menu">
        <item name="android:textSize">@dimen/abc_text_size_menu_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult" parent="">
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHint">?android:textColorHint</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Title">
        <item name="android:textSize">18sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Small">
        <item name="android:textSize">@dimen/abc_text_size_small_material</item>
        <item name="android:textColor">?android:attr/textColorTertiary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Small.Inverse">
        <item name="android:textColor">?android:attr/textColorTertiaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subhead_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Tooltip">
        <item name="android:textSize">14sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="TextAppearance.AppCompat.Button">
        <item name="android:textColor">?attr/actionMenuTextColor</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="TextAppearance.AppCompat.Subhead.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorSecondaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="TextAppearance.AppCompat.Title.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="TextAppearance.AppCompat.Widget.ActionBar.Subtitle"/>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="TextAppearance.AppCompat.Widget.ActionBar.Title"/>
    <style name="Base.TextAppearance.AppCompat.Widget.Button" parent="TextAppearance.AppCompat.Button"/>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="Base.TextAppearance.AppCompat.Widget.Button">
        <item name="android:textColor">@color/abc_btn_colored_borderless_text_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Colored">
        <item name="android:textColor">@color/abc_btn_colored_text_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" parent="TextAppearance.AppCompat.Button">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.DropDownItem" parent="android:TextAppearance.Small">
        <item name="android:textColor">?android:attr/textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_menu_header_material</item>
        <item name="android:textColor">?attr/colorAccent</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="TextAppearance.AppCompat.Button"/>
    <style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="android:TextAppearance.Medium">
        <item name="android:textColor">?android:attr/textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="TextAppearance.AppCompat.Widget.ActionBar.Subtitle">
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
    </style>
    <style name="Base.Theme.AppCompat" parent="Base.V7.Theme.AppCompat">
    </style>
    <style name="Base.Theme.AppCompat.CompactMenu" parent="">
        <item name="android:itemTextAppearance">?android:attr/textAppearanceMedium</item>
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog" parent="Base.V7.Theme.AppCompat.Dialog"/>
    <style name="Base.Theme.AppCompat.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.FixedSize">
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.MinWidth">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.DialogWhenLarge" parent="Theme.AppCompat"/>
    <style name="Base.Theme.AppCompat.Light" parent="Base.V7.Theme.AppCompat.Light">
    </style>
    <style name="Base.Theme.AppCompat.Light.DarkActionBar" parent="Base.Theme.AppCompat.Light">
        <item name="actionBarPopupTheme">@style/ThemeOverlay.AppCompat.Light</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.Dark.ActionBar</item>

        <!-- Panel attributes -->
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>

        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog" parent="Base.V7.Theme.AppCompat.Light.Dialog"/>
    <style name="Base.Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.FixedSize">
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.MinWidth">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.DialogWhenLarge" parent="Theme.AppCompat.Light"/>
    <style name="Base.ThemeOverlay.AppCompat" parent="Platform.ThemeOverlay.AppCompat"/>
    <style name="Base.ThemeOverlay.AppCompat.ActionBar">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark" parent="Platform.ThemeOverlay.AppCompat.Dark">
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>

        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>

        <!-- Used by MediaRouter -->
        <item name="isLightTheme">false</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark.ActionBar">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dialog" parent="Base.V7.ThemeOverlay.AppCompat.Dialog"/>
    <style name="Base.ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Light" parent="Platform.ThemeOverlay.AppCompat.Light">
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>

        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>

        <!-- Used by MediaRouter -->
        <item name="isLightTheme">true</item>
    </style>
    <style name="Base.V7.Theme.AppCompat" parent="Platform.AppCompat">
        <item name="viewInflaterClass">androidx.appcompat.app.AppCompatViewInflater</item>
        <item name="windowNoTitle">false</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="actionBarPopupTheme">@null</item>

        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>

        <!-- Used by MediaRouter -->
        <item name="isLightTheme">false</item>

        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>

        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>

        <!-- Action Bar Styles -->
        <item name="actionBarTabStyle">@style/Widget.AppCompat.ActionBar.TabView</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.ActionBar.TabBar</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.ActionBar.TabText</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.PopupMenu.Overflow</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.ActionBar.Solid</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>

        <!-- Dropdown Spinner Attributes -->
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>

        <!-- Action Mode -->
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>

        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>

        <!-- Panel attributes -->
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>

        <!-- List attributes -->
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>

        <!-- Spinner styles -->
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>

        <!-- Required for use of support_simple_spinner_dropdown_item.xml -->
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>

        <!-- Popup Menu styles -->
        <item name="popupMenuStyle">@style/Widget.AppCompat.PopupMenu</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Large</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Small</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>

        <!-- SearchView attributes -->
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>

        <!-- ShareActionProvider attributes -->
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>

        <!-- Toolbar styles -->
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>

        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="android:textViewStyle">@style/Widget.AppCompat.TextView</item>

        <!-- Color palette -->
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorAccent">@color/accent_material_dark</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>

        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>

        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>

        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>

        <!-- Button styles -->
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>

        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>

        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>

        <!-- Dialog attributes -->
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>

        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="listDividerAlertDialog">@null</item>

        <!-- Define these here; ContextThemeWrappers around themes that define them should
             always clear these values. -->
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>

        <!-- Tooltip attributes -->
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_light</item>
        <item name="tooltipForegroundColor">@color/foreground_material_light</item>

        <item name="colorError">@color/error_color_material_dark</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Dialog" parent="Base.Theme.AppCompat">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:colorBackgroundCacheHint">@null</item>

        <item name="android:windowFrame">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>

        <item name="listPreferredItemPaddingLeft">24dip</item>
        <item name="listPreferredItemPaddingRight">24dip</item>

        <item name="android:listDivider">@null</item>

        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light" parent="Platform.AppCompat.Light">
        <item name="viewInflaterClass">androidx.appcompat.app.AppCompatViewInflater</item>

        <item name="windowNoTitle">false</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="actionBarPopupTheme">@null</item>

        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>

        <!-- Used by MediaRouter -->
        <item name="isLightTheme">true</item>

        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>

        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>

        <!-- Action Bar Styles -->
        <item name="actionBarTabStyle">@style/Widget.AppCompat.Light.ActionBar.TabView</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.Light.ActionBar.TabBar</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.Light.ActionBar.TabText</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.Light.PopupMenu.Overflow</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.Light.ActionBar.Solid</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>

        <!-- Action Mode -->
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>

        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>

        <!-- Dropdown Spinner Attributes -->
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>

        <!-- Panel attributes -->
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_light</item>

        <!-- List attributes -->
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>

        <!-- Spinner styles -->
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>

        <!-- Required for use of support_simple_spinner_dropdown_item.xml -->
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>

        <!-- Popup Menu styles -->
        <item name="popupMenuStyle">@style/Widget.AppCompat.Light.PopupMenu</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>

        <!-- SearchView attributes -->
        <item name="searchViewStyle">@style/Widget.AppCompat.Light.SearchView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>

        <!-- ShareActionProvider attributes -->
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>

        <!-- Toolbar styles -->
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>

        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="android:textViewStyle">@style/Widget.AppCompat.TextView</item>

        <!-- Color palette -->
        <item name="colorPrimaryDark">@color/primary_dark_material_light</item>
        <item name="colorPrimary">@color/primary_material_light</item>
        <item name="colorAccent">@color/accent_material_light</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>

        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>

        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>

        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>

        <!-- Button styles -->
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>

        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>

        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>

        <!-- Dialog attributes -->
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>

        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat.Light</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="listDividerAlertDialog">@null</item>

        <!-- Define these here; ContextThemeWrappers around themes that define them should
             always clear these values. -->
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>

        <!-- Tooltip attributes -->
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_dark</item>
        <item name="tooltipForegroundColor">@color/foreground_material_dark</item>

        <item name="colorError">@color/error_color_material_light</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light.Dialog" parent="Base.Theme.AppCompat.Light">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:colorBackgroundCacheHint">@null</item>

        <item name="android:windowFrame">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>

        <item name="listPreferredItemPaddingLeft">24dip</item>
        <item name="listPreferredItemPaddingRight">24dip</item>

        <item name="android:listDivider">@null</item>

        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>
    <style name="Base.V7.ThemeOverlay.AppCompat.Dialog" parent="Base.ThemeOverlay.AppCompat">
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>

        <item name="android:windowFrame">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>

        <item name="listPreferredItemPaddingLeft">24dip</item>
        <item name="listPreferredItemPaddingRight">24dip</item>

        <item name="android:listDivider">@null</item>

        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>

        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.AutoCompleteTextView" parent="android:Widget.AutoCompleteTextView">
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:background">?attr/editTextBackground</item>
        <item name="android:textColor">?attr/editTextColor</item>
        <item name="android:textAppearance">?android:attr/textAppearanceMediumInverse</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.EditText" parent="android:Widget.EditText">
        <item name="android:background">?attr/editTextBackground</item>
        <item name="android:textColor">?attr/editTextColor</item>
        <item name="android:textAppearance">?android:attr/textAppearanceMediumInverse</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.Toolbar" parent="android:Widget">
        <item name="titleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Title</item>
        <item name="subtitleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle</item>
        <item name="android:minHeight">?attr/actionBarSize</item>
        <item name="titleMargin">4dp</item>
        <item name="maxButtonHeight">@dimen/abc_action_bar_default_height_material</item>
        <item name="buttonGravity">top</item>
        <item name="collapseIcon">?attr/homeAsUpIndicator</item>
        <item name="collapseContentDescription">@string/abc_toolbar_collapse_description</item>
        <item name="contentInsetStart">16dp</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="android:paddingLeft">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_default_padding_end_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar" parent="">
        <item name="displayOptions">showTitle</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="height">?attr/actionBarSize</item>

        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Title</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle</item>

        <item name="background">@null</item>
        <item name="backgroundStacked">@null</item>
        <item name="backgroundSplit">@null</item>

        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>

        <item name="android:gravity">center_vertical</item>
        <item name="contentInsetStart">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="contentInsetEnd">@dimen/abc_action_bar_content_inset_material</item>
        <item name="elevation">@dimen/abc_action_bar_elevation_material</item>
        <item name="popupTheme">?attr/actionBarPopupTheme</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.Solid">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabBar" parent="">
        <item name="divider">?attr/actionBarDivider</item>
        <item name="showDividers">middle</item>
        <item name="dividerPadding">8dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabText" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:ellipsize">marquee</item>
        <item name="android:maxLines">2</item>
        <item name="android:maxWidth">180dp</item>
        <item name="textAllCaps">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabView" parent="">
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
        <item name="android:gravity">center_horizontal</item>
        <item name="android:paddingLeft">16dip</item>
        <item name="android:paddingRight">16dip</item>
        <item name="android:layout_width">0dip</item>
        <item name="android:layout_weight">1</item>
        <item name="android:minWidth">80dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton" parent="RtlUnderlay.Widget.AppCompat.ActionButton">
        <item name="android:background">?attr/actionBarItemBackground</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
        <item name="android:scaleType">center</item>
        <item name="android:gravity">center</item>
        <item name="android:maxLines">2</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.CloseMode">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:minWidth">56dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow">
        <item name="srcCompat">@drawable/abc_ic_menu_overflow_material</item>
        <item name="android:background">?attr/actionBarItemBackground</item>
        <item name="android:contentDescription">@string/abc_action_menu_overflow_description</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_overflow_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionMode" parent="">
        <item name="background">?attr/actionModeBackground</item>
        <item name="backgroundSplit">?attr/actionModeSplitBackground</item>
        <item name="height">?attr/actionBarSize</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Title</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle</item>
        <item name="closeItemLayout">@layout/abc_action_mode_close_item_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActivityChooserView" parent="">
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/abc_ab_share_pack_mtrl_alpha</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="showDividers">middle</item>
        <item name="dividerPadding">6dip</item>
    </style>
    <style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="Base.V7.Widget.AppCompat.AutoCompleteTextView"/>
    <style name="Base.Widget.AppCompat.Button" parent="android:Widget">
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
        <item name="android:textAppearance">?android:attr/textAppearanceButton</item>
        <item name="android:minHeight">48dip</item>
        <item name="android:minWidth">88dip</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center_vertical|center_horizontal</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/abc_btn_borderless_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:minWidth">64dp</item>
        <item name="android:minHeight">@dimen/abc_alert_dialog_button_bar_height</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Colored">
        <item name="android:background">@drawable/abc_btn_colored_material</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Colored</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Small">
        <item name="android:minHeight">48dip</item>
        <item name="android:minWidth">48dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar" parent="android:Widget">
        <item name="android:background">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar.AlertDialog"/>
    <style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="android:Widget.CompoundButton.CheckBox">
        <item name="android:button">?android:attr/listChoiceIndicatorMultiple</item>
        <item name="buttonCompat">?attr/listChoiceIndicatorMultipleAnimated</item>
        <item name="android:background">?attr/controlBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="android:Widget.CompoundButton.RadioButton">
        <item name="android:button">?android:attr/listChoiceIndicatorSingle</item>
        <item name="buttonCompat">?attr/listChoiceIndicatorSingleAnimated</item>
        <item name="android:background">?attr/controlBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.Switch" parent="android:Widget.CompoundButton">
        <item name="track">@drawable/abc_switch_track_mtrl_alpha</item>
        <item name="android:thumb">@drawable/abc_switch_thumb_material</item>
        <item name="switchTextAppearance">@style/TextAppearance.AppCompat.Widget.Switch</item>
        <item name="android:background">?attr/controlBackground</item>
        <item name="showText">false</item>
        <item name="switchPadding">@dimen/abc_switch_padding</item>
        <item name="android:textOn">@string/abc_capital_on</item>
        <item name="android:textOff">@string/abc_capital_off</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle" parent="Base.Widget.AppCompat.DrawerArrowToggle.Common">
        <item name="barLength">18dp</item>
        <item name="gapBetweenBars">3dp</item>
        <item name="drawableSize">24dp</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle.Common" parent="">
        <item name="color">?android:attr/textColorSecondary</item>
        <item name="spinBars">true</item>
        <item name="thickness">2dp</item>
        <item name="arrowShaftLength">16dp</item>
        <item name="arrowHeadLength">8dp</item>
    </style>
    <style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.DropDownItem</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
        <item name="android:gravity">center_vertical</item>
    </style>
    <style name="Base.Widget.AppCompat.EditText" parent="Base.V7.Widget.AppCompat.EditText"/>
    <style name="Base.Widget.AppCompat.ImageButton" parent="android:Widget.ImageButton">
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar" parent="Base.Widget.AppCompat.ActionBar">
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.Solid">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabBar" parent="Base.Widget.AppCompat.ActionBar.TabBar">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="Base.Widget.AppCompat.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="Base.Widget.AppCompat.Light.ActionBar.TabText">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium.Inverse</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="Base.Widget.AppCompat.ActionBar.TabView">
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow">
        <item name="overlapAnchor">true</item>
        <item name="android:dropDownHorizontalOffset">-4dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ListMenuView" parent="android:Widget">
        <item name="subMenuArrow">@drawable/abc_ic_arrow_drop_right_black_24dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ListPopupWindow" parent="">
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:dropDownVerticalOffset">0dip</item>
        <item name="android:dropDownHorizontalOffset">0dip</item>
        <item name="android:dropDownWidth">wrap_content</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView" parent="android:Widget.ListView">
        <item name="android:listSelector">?attr/listChoiceBackgroundIndicator</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView.DropDown">
        <item name="android:divider">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView.Menu" parent="android:Widget.ListView.Menu">
        <item name="android:listSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:divider">?attr/dividerHorizontal</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu.Overflow">
        <item name="overlapAnchor">true</item>
        <item name="android:dropDownHorizontalOffset">-4dip</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupWindow" parent="android:Widget.PopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar" parent="android:Widget.Holo.ProgressBar">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="android:Widget.Holo.ProgressBar.Horizontal">
    </style>
    <style name="Base.Widget.AppCompat.RatingBar" parent="android:Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/abc_ratingbar_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_material</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Indicator" parent="android:Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:minHeight">36dp</item>
        <item name="android:maxHeight">36dp</item>
        <item name="android:isIndicator">true</item>
        <item name="android:thumb">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Small" parent="android:Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:minHeight">16dp</item>
        <item name="android:maxHeight">16dp</item>
        <item name="android:isIndicator">true</item>
        <item name="android:thumb">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView" parent="android:Widget">
        <item name="layout">@layout/abc_search_view</item>
        <item name="queryBackground">@drawable/abc_textfield_search_material</item>
        <item name="submitBackground">@drawable/abc_textfield_search_material</item>
        <item name="closeIcon">@drawable/abc_ic_clear_material</item>
        <item name="searchIcon">@drawable/abc_ic_search_api_material</item>
        <item name="searchHintIcon">@drawable/abc_ic_search_api_material</item>
        <item name="goIcon">@drawable/abc_ic_go_search_api_material</item>
        <item name="voiceIcon">@drawable/abc_ic_voice_search_api_material</item>
        <item name="commitIcon">@drawable/abc_ic_commit_search_api_mtrl_alpha</item>
        <item name="suggestionRowLayout">@layout/abc_search_dropdown_item_icons_2line</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView.ActionBar">
        <item name="queryBackground">@null</item>
        <item name="submitBackground">@null</item>
        <item name="searchHintIcon">@null</item>
        <item name="defaultQueryHint">@string/abc_search_hint</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar" parent="android:Widget">
        <item name="android:indeterminateOnly">false</item>
        <item name="android:progressDrawable">@drawable/abc_seekbar_track_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_seekbar_track_material</item>
        <item name="android:thumb">@drawable/abc_seekbar_thumb_material</item>
        <item name="android:focusable">true</item>
        <item name="android:paddingLeft">16dip</item>
        <item name="android:paddingRight">16dip</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar.Discrete">
        <item name="tickMark">@drawable/abc_seekbar_tick_mark_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner" parent="Platform.Widget.AppCompat.Spinner">
        <item name="android:background">@drawable/abc_spinner_mtrl_am_alpha</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:dropDownVerticalOffset">0dip</item>
        <item name="android:dropDownHorizontalOffset">0dip</item>
        <item name="android:dropDownWidth">wrap_content</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">left|start|center_vertical</item>
        <item name="overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner.Underlined">
        <item name="android:background">@drawable/abc_spinner_textfield_background_material</item>
    </style>
    <style name="Base.Widget.AppCompat.TextView" parent="android:Widget.TextView"/>
    <style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="android:Widget.TextView.SpinnerItem">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
    </style>
    <style name="Base.Widget.AppCompat.Toolbar" parent="Base.V7.Widget.AppCompat.Toolbar"/>
    <style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="android:Widget">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:minWidth">56dp</item>
        <item name="android:scaleType">center</item>
    </style>
    <style name="Platform.AppCompat" parent="android:Theme.Holo">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>

        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:borderlessButtonStyle">?attr/borderlessButtonStyle</item>

        <!-- Window colors -->
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_dark</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowBackground">@color/background_material_dark</item>

        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_light</item>
        <item name="android:textColorLink">?attr/colorAccent</item>
        <item name="android:textColorLinkInverse">?attr/colorAccent</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>

        <!-- Text styles -->
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>

        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="listChoiceIndicatorSingleAnimated">@drawable/abc_btn_radio_material_anim</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>
        <item name="listChoiceIndicatorMultipleAnimated">@drawable/abc_btn_check_material_anim</item>

        <!-- List attributes -->
        <item name="android:textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="android:textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <!-- textAppearanceListItemSecondary is 21+ only so not safe to apply here-->
        <item name="android:listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="android:listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="android:listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>

        <item name="android:actionModeCutDrawable">?actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?actionModePasteDrawable</item>
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>

        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl_dark</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl_dark</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl_dark</item>
    </style>
    <style name="Platform.AppCompat.Light" parent="android:Theme.Holo.Light">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>

        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:borderlessButtonStyle">?attr/borderlessButtonStyle</item>

        <!-- Window colors -->
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_light</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowBackground">@color/background_material_light</item>

        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_dark</item>
        <item name="android:textColorLink">?attr/colorAccent</item>
        <item name="android:textColorLinkInverse">?attr/colorAccent</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>

        <!-- Text styles -->
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>

        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="listChoiceIndicatorSingleAnimated">@drawable/abc_btn_radio_material_anim</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>
        <item name="listChoiceIndicatorMultipleAnimated">@drawable/abc_btn_check_material_anim</item>

        <!-- List attributes -->
        <item name="android:textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="android:textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <!-- textAppearanceListItemSecondary is 21+ only so not safe to apply here-->
        <item name="android:listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="android:listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="android:listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>

        <item name="android:actionModeCutDrawable">?actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?actionModePasteDrawable</item>
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>

        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl_light</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl_light</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl_light</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat" parent=""/>
    <style name="Platform.ThemeOverlay.AppCompat.Dark">
        <!-- Action Bar styles -->
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>

        <!-- SearchView styles -->
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Light">
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>

        <!-- SearchView attributes -->
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.Light.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
    </style>
    <style name="Platform.Widget.AppCompat.Spinner" parent="android:Widget.Holo.Spinner"/>
    <style name="RtlOverlay.DialogWindowTitle.AppCompat" parent="Base.DialogWindowTitle.AppCompat">
    </style>
    <style name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" parent="android:Widget">
        <item name="android:layout_gravity">center_vertical|left</item>
        <item name="android:paddingRight">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" parent="android:Widget">
        <item name="android:layout_marginRight">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem" parent="android:Widget">
        <item name="android:paddingRight">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" parent="android:Widget">
        <item name="android:layout_marginLeft">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" parent="android:Widget">
        <item name="android:layout_marginLeft">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" parent="android:Widget">
        <item name="android:layout_marginLeft">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" parent="android:Widget">
        <item name="android:layout_alignParentLeft">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" parent="android:Widget">
        <item name="android:layout_marginLeft">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown" parent="android:Widget">
        <item name="android:paddingLeft">@dimen/abc_dropdownitem_text_padding_left</item>
        <item name="android:paddingRight">4dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" parent="android:Widget">
        <item name="android:layout_alignParentLeft">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" parent="android:Widget">
        <item name="android:layout_toLeftOf">@id/edit_query</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" parent="android:Widget">
        <item name="android:layout_alignParentRight">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" parent="Base.Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:layout_toLeftOf">@android:id/icon2</item>
        <item name="android:layout_toRightOf">@android:id/icon1</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" parent="android:Widget">
        <item name="android:layout_marginLeft">@dimen/abc_dropdownitem_text_padding_left</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton" parent="android:Widget">
        <item name="android:paddingLeft">12dp</item>
        <item name="android:paddingRight">12dp</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" parent="Base.Widget.AppCompat.ActionButton">
        <item name="android:paddingLeft">@dimen/abc_action_bar_overflow_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_overflow_padding_end_material</item>
    </style>
    <style name="TextAppearance.AppCompat" parent="Base.TextAppearance.AppCompat"/>
    <style name="TextAppearance.AppCompat.Body1" parent="Base.TextAppearance.AppCompat.Body1"/>
    <style name="TextAppearance.AppCompat.Body2" parent="Base.TextAppearance.AppCompat.Body2"/>
    <style name="TextAppearance.AppCompat.Button" parent="Base.TextAppearance.AppCompat.Button"/>
    <style name="TextAppearance.AppCompat.Caption" parent="Base.TextAppearance.AppCompat.Caption"/>
    <style name="TextAppearance.AppCompat.Display1" parent="Base.TextAppearance.AppCompat.Display1"/>
    <style name="TextAppearance.AppCompat.Display2" parent="Base.TextAppearance.AppCompat.Display2"/>
    <style name="TextAppearance.AppCompat.Display3" parent="Base.TextAppearance.AppCompat.Display3"/>
    <style name="TextAppearance.AppCompat.Display4" parent="Base.TextAppearance.AppCompat.Display4"/>
    <style name="TextAppearance.AppCompat.Headline" parent="Base.TextAppearance.AppCompat.Headline"/>
    <style name="TextAppearance.AppCompat.Inverse" parent="Base.TextAppearance.AppCompat.Inverse"/>
    <style name="TextAppearance.AppCompat.Large" parent="Base.TextAppearance.AppCompat.Large"/>
    <style name="TextAppearance.AppCompat.Large.Inverse" parent="Base.TextAppearance.AppCompat.Large.Inverse"/>
    <style name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" parent="TextAppearance.AppCompat.SearchResult.Subtitle"/>
    <style name="TextAppearance.AppCompat.Light.SearchResult.Title" parent="TextAppearance.AppCompat.SearchResult.Title"/>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="TextAppearance.AppCompat.Widget.PopupMenu.Large"/>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="TextAppearance.AppCompat.Widget.PopupMenu.Small"/>
    <style name="TextAppearance.AppCompat.Medium" parent="Base.TextAppearance.AppCompat.Medium"/>
    <style name="TextAppearance.AppCompat.Medium.Inverse" parent="Base.TextAppearance.AppCompat.Medium.Inverse"/>
    <style name="TextAppearance.AppCompat.Menu" parent="Base.TextAppearance.AppCompat.Menu"/>
    <style name="TextAppearance.AppCompat.SearchResult.Subtitle" parent="Base.TextAppearance.AppCompat.SearchResult.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.SearchResult.Title" parent="Base.TextAppearance.AppCompat.SearchResult.Title">
    </style>
    <style name="TextAppearance.AppCompat.Small" parent="Base.TextAppearance.AppCompat.Small"/>
    <style name="TextAppearance.AppCompat.Small.Inverse" parent="Base.TextAppearance.AppCompat.Small.Inverse"/>
    <style name="TextAppearance.AppCompat.Subhead" parent="Base.TextAppearance.AppCompat.Subhead"/>
    <style name="TextAppearance.AppCompat.Subhead.Inverse" parent="Base.TextAppearance.AppCompat.Subhead.Inverse"/>
    <style name="TextAppearance.AppCompat.Title" parent="Base.TextAppearance.AppCompat.Title"/>
    <style name="TextAppearance.AppCompat.Title.Inverse" parent="Base.TextAppearance.AppCompat.Title.Inverse"/>
    <style name="TextAppearance.AppCompat.Tooltip" parent="Base.TextAppearance.AppCompat.Tooltip"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Title"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" parent="TextAppearance.AppCompat.Widget.ActionMode.Subtitle"/>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title" parent="Base.TextAppearance.AppCompat.Widget.ActionMode.Title">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" parent="TextAppearance.AppCompat.Widget.ActionMode.Title"/>
    <style name="TextAppearance.AppCompat.Widget.Button" parent="Base.TextAppearance.AppCompat.Widget.Button"/>
    <style name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored"/>
    <style name="TextAppearance.AppCompat.Widget.Button.Colored" parent="Base.TextAppearance.AppCompat.Widget.Button.Colored"/>
    <style name="TextAppearance.AppCompat.Widget.Button.Inverse" parent="Base.TextAppearance.AppCompat.Widget.Button.Inverse"/>
    <style name="TextAppearance.AppCompat.Widget.DropDownItem" parent="Base.TextAppearance.AppCompat.Widget.DropDownItem">
    </style>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header"/>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large"/>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small"/>
    <style name="TextAppearance.AppCompat.Widget.Switch" parent="Base.TextAppearance.AppCompat.Widget.Switch"/>
    <style name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem"/>
    <style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.StatusBar.EventContent"/>
    <style name="TextAppearance.Compat.Notification.Info">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Info.Media"/>
    <style name="TextAppearance.Compat.Notification.Line2" parent="TextAppearance.Compat.Notification.Info"/>
    <style name="TextAppearance.Compat.Notification.Line2.Media" parent="TextAppearance.Compat.Notification.Info.Media"/>
    <style name="TextAppearance.Compat.Notification.Media"/>
    <style name="TextAppearance.Compat.Notification.Time">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Time.Media"/>
    <style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.StatusBar.EventContent.Title"/>
    <style name="TextAppearance.Compat.Notification.Title.Media"/>
    <style name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item">
    </style>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle">
    </style>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Title" parent="Base.TextAppearance.Widget.AppCompat.Toolbar.Title">
    </style>
    <style name="Theme.AppCompat" parent="Base.Theme.AppCompat"/>
    <style name="Theme.AppCompat.CompactMenu" parent="Base.Theme.AppCompat.CompactMenu"/>
    <style name="Theme.AppCompat.DayNight" parent="Theme.AppCompat.Light"/>
    <style name="Theme.AppCompat.DayNight.DarkActionBar" parent="Theme.AppCompat.Light.DarkActionBar"/>
    <style name="Theme.AppCompat.DayNight.Dialog" parent="Theme.AppCompat.Light.Dialog"/>
    <style name="Theme.AppCompat.DayNight.Dialog.Alert" parent="Theme.AppCompat.Light.Dialog.Alert"/>
    <style name="Theme.AppCompat.DayNight.Dialog.MinWidth" parent="Theme.AppCompat.Light.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.DayNight.DialogWhenLarge" parent="Theme.AppCompat.Light.DialogWhenLarge"/>
    <style name="Theme.AppCompat.DayNight.NoActionBar" parent="Theme.AppCompat.Light.NoActionBar"/>
    <style name="Theme.AppCompat.Dialog" parent="Base.Theme.AppCompat.Dialog"/>
    <style name="Theme.AppCompat.Dialog.Alert" parent="Base.Theme.AppCompat.Dialog.Alert"/>
    <style name="Theme.AppCompat.Dialog.MinWidth" parent="Base.Theme.AppCompat.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.DialogWhenLarge" parent="Base.Theme.AppCompat.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.Light" parent="Base.Theme.AppCompat.Light"/>
    <style name="Theme.AppCompat.Light.DarkActionBar" parent="Base.Theme.AppCompat.Light.DarkActionBar"/>
    <style name="Theme.AppCompat.Light.Dialog" parent="Base.Theme.AppCompat.Light.Dialog"/>
    <style name="Theme.AppCompat.Light.Dialog.Alert" parent="Base.Theme.AppCompat.Light.Dialog.Alert"/>
    <style name="Theme.AppCompat.Light.Dialog.MinWidth" parent="Base.Theme.AppCompat.Light.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.Light.DialogWhenLarge" parent="Base.Theme.AppCompat.Light.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.Light.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.AppCompat.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="ThemeOverlay.AppCompat" parent="Base.ThemeOverlay.AppCompat"/>
    <style name="ThemeOverlay.AppCompat.ActionBar" parent="Base.ThemeOverlay.AppCompat.ActionBar"/>
    <style name="ThemeOverlay.AppCompat.Dark" parent="Base.ThemeOverlay.AppCompat.Dark"/>
    <style name="ThemeOverlay.AppCompat.Dark.ActionBar" parent="Base.ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="ThemeOverlay.AppCompat.DayNight" parent="ThemeOverlay.AppCompat.Light"/>
    <style name="ThemeOverlay.AppCompat.DayNight.ActionBar">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="ThemeOverlay.AppCompat.Dialog" parent="Base.ThemeOverlay.AppCompat.Dialog"/>
    <style name="ThemeOverlay.AppCompat.Dialog.Alert" parent="Base.ThemeOverlay.AppCompat.Dialog.Alert"/>
    <style name="ThemeOverlay.AppCompat.Light" parent="Base.ThemeOverlay.AppCompat.Light"/>
    <style name="Widget.AppCompat.ActionBar" parent="Base.Widget.AppCompat.ActionBar">
    </style>
    <style name="Widget.AppCompat.ActionBar.Solid" parent="Base.Widget.AppCompat.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabBar" parent="Base.Widget.AppCompat.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabText" parent="Base.Widget.AppCompat.ActionBar.TabText">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabView" parent="Base.Widget.AppCompat.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.ActionButton" parent="Base.Widget.AppCompat.ActionButton"/>
    <style name="Widget.AppCompat.ActionButton.CloseMode" parent="Base.Widget.AppCompat.ActionButton.CloseMode"/>
    <style name="Widget.AppCompat.ActionButton.Overflow" parent="Base.Widget.AppCompat.ActionButton.Overflow"/>
    <style name="Widget.AppCompat.ActionMode" parent="Base.Widget.AppCompat.ActionMode">
    </style>
    <style name="Widget.AppCompat.ActivityChooserView" parent="Base.Widget.AppCompat.ActivityChooserView">
    </style>
    <style name="Widget.AppCompat.AutoCompleteTextView" parent="Base.Widget.AppCompat.AutoCompleteTextView">
    </style>
    <style name="Widget.AppCompat.Button" parent="Base.Widget.AppCompat.Button"/>
    <style name="Widget.AppCompat.Button.Borderless" parent="Base.Widget.AppCompat.Button.Borderless"/>
    <style name="Widget.AppCompat.Button.Borderless.Colored" parent="Base.Widget.AppCompat.Button.Borderless.Colored"/>
    <style name="Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog"/>
    <style name="Widget.AppCompat.Button.Colored" parent="Base.Widget.AppCompat.Button.Colored"/>
    <style name="Widget.AppCompat.Button.Small" parent="Base.Widget.AppCompat.Button.Small"/>
    <style name="Widget.AppCompat.ButtonBar" parent="Base.Widget.AppCompat.ButtonBar"/>
    <style name="Widget.AppCompat.ButtonBar.AlertDialog" parent="Base.Widget.AppCompat.ButtonBar.AlertDialog"/>
    <style name="Widget.AppCompat.CompoundButton.CheckBox" parent="Base.Widget.AppCompat.CompoundButton.CheckBox"/>
    <style name="Widget.AppCompat.CompoundButton.RadioButton" parent="Base.Widget.AppCompat.CompoundButton.RadioButton"/>
    <style name="Widget.AppCompat.CompoundButton.Switch" parent="Base.Widget.AppCompat.CompoundButton.Switch"/>
    <style name="Widget.AppCompat.DrawerArrowToggle" parent="Base.Widget.AppCompat.DrawerArrowToggle">
        <item name="color">?attr/colorControlNormal</item>
    </style>
    <style name="Widget.AppCompat.DropDownItem.Spinner" parent="RtlOverlay.Widget.AppCompat.Search.DropDown.Text"/>
    <style name="Widget.AppCompat.EditText" parent="Base.Widget.AppCompat.EditText"/>
    <style name="Widget.AppCompat.ImageButton" parent="Base.Widget.AppCompat.ImageButton"/>
    <style name="Widget.AppCompat.Light.ActionBar" parent="Base.Widget.AppCompat.Light.ActionBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.Solid" parent="Base.Widget.AppCompat.Light.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.Solid.Inverse"/>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar" parent="Base.Widget.AppCompat.Light.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse"/>
    <style name="Widget.AppCompat.Light.ActionBar.TabText" parent="Base.Widget.AppCompat.Light.ActionBar.TabText">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabView" parent="Base.Widget.AppCompat.Light.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabView.Inverse"/>
    <style name="Widget.AppCompat.Light.ActionButton" parent="Widget.AppCompat.ActionButton"/>
    <style name="Widget.AppCompat.Light.ActionButton.CloseMode" parent="Widget.AppCompat.ActionButton.CloseMode"/>
    <style name="Widget.AppCompat.Light.ActionButton.Overflow" parent="Widget.AppCompat.ActionButton.Overflow"/>
    <style name="Widget.AppCompat.Light.ActionMode.Inverse" parent="Widget.AppCompat.ActionMode"/>
    <style name="Widget.AppCompat.Light.ActivityChooserView" parent="Widget.AppCompat.ActivityChooserView"/>
    <style name="Widget.AppCompat.Light.AutoCompleteTextView" parent="Widget.AppCompat.AutoCompleteTextView"/>
    <style name="Widget.AppCompat.Light.DropDownItem.Spinner" parent="Widget.AppCompat.DropDownItem.Spinner"/>
    <style name="Widget.AppCompat.Light.ListPopupWindow" parent="Widget.AppCompat.ListPopupWindow"/>
    <style name="Widget.AppCompat.Light.ListView.DropDown" parent="Widget.AppCompat.ListView.DropDown"/>
    <style name="Widget.AppCompat.Light.PopupMenu" parent="Base.Widget.AppCompat.Light.PopupMenu"/>
    <style name="Widget.AppCompat.Light.PopupMenu.Overflow" parent="Base.Widget.AppCompat.Light.PopupMenu.Overflow">
    </style>
    <style name="Widget.AppCompat.Light.SearchView" parent="Widget.AppCompat.SearchView"/>
    <style name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" parent="Widget.AppCompat.Spinner.DropDown.ActionBar"/>
    <style name="Widget.AppCompat.ListMenuView" parent="Base.Widget.AppCompat.ListMenuView"/>
    <style name="Widget.AppCompat.ListPopupWindow" parent="Base.Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Widget.AppCompat.ListView" parent="Base.Widget.AppCompat.ListView"/>
    <style name="Widget.AppCompat.ListView.DropDown" parent="Base.Widget.AppCompat.ListView.DropDown"/>
    <style name="Widget.AppCompat.ListView.Menu" parent="Base.Widget.AppCompat.ListView.Menu"/>
    <style name="Widget.AppCompat.PopupMenu" parent="Base.Widget.AppCompat.PopupMenu"/>
    <style name="Widget.AppCompat.PopupMenu.Overflow" parent="Base.Widget.AppCompat.PopupMenu.Overflow">
    </style>
    <style name="Widget.AppCompat.PopupWindow" parent="Base.Widget.AppCompat.PopupWindow">
    </style>
    <style name="Widget.AppCompat.ProgressBar" parent="Base.Widget.AppCompat.ProgressBar">
    </style>
    <style name="Widget.AppCompat.ProgressBar.Horizontal" parent="Base.Widget.AppCompat.ProgressBar.Horizontal">
    </style>
    <style name="Widget.AppCompat.RatingBar" parent="Base.Widget.AppCompat.RatingBar"/>
    <style name="Widget.AppCompat.RatingBar.Indicator" parent="Base.Widget.AppCompat.RatingBar.Indicator"/>
    <style name="Widget.AppCompat.RatingBar.Small" parent="Base.Widget.AppCompat.RatingBar.Small"/>
    <style name="Widget.AppCompat.SearchView" parent="Base.Widget.AppCompat.SearchView"/>
    <style name="Widget.AppCompat.SearchView.ActionBar" parent="Base.Widget.AppCompat.SearchView.ActionBar"/>
    <style name="Widget.AppCompat.SeekBar" parent="Base.Widget.AppCompat.SeekBar"/>
    <style name="Widget.AppCompat.SeekBar.Discrete" parent="Base.Widget.AppCompat.SeekBar.Discrete"/>
    <style name="Widget.AppCompat.Spinner" parent="Base.Widget.AppCompat.Spinner"/>
    <style name="Widget.AppCompat.Spinner.DropDown"/>
    <style name="Widget.AppCompat.Spinner.DropDown.ActionBar"/>
    <style name="Widget.AppCompat.Spinner.Underlined" parent="Base.Widget.AppCompat.Spinner.Underlined"/>
    <style name="Widget.AppCompat.TextView" parent="Base.Widget.AppCompat.TextView"/>
    <style name="Widget.AppCompat.TextView.SpinnerItem" parent="Base.Widget.AppCompat.TextView.SpinnerItem"/>
    <style name="Widget.AppCompat.Toolbar" parent="Base.Widget.AppCompat.Toolbar"/>
    <style name="Widget.AppCompat.Toolbar.Button.Navigation" parent="Base.Widget.AppCompat.Toolbar.Button.Navigation"/>
    <style name="Widget.Compat.NotificationActionContainer" parent=""/>
    <style name="Widget.Compat.NotificationActionText" parent=""/>
    <style name="Widget.Support.CoordinatorLayout" parent="android:Widget">
        <item name="statusBarBackground">#000000</item>
    </style>
    <declare-styleable name="ActionBar">
        <!-- The type of navigation to use. -->
        <attr name="navigationMode">
            <!-- Normal static title text -->
            <enum name="normal" value="0"/>
            <!-- The action bar will use a selection list for navigation. -->
            <enum name="listMode" value="1"/>
            <!-- The action bar will use a series of horizontal tabs for navigation. -->
            <enum name="tabMode" value="2"/>
        </attr>
        <!-- Options affecting how the action bar is displayed. -->
        <attr name="displayOptions">
            <flag name="none" value="0"/>
            <flag name="useLogo" value="0x1"/>
            <flag name="showHome" value="0x2"/>
            <flag name="homeAsUp" value="0x4"/>
            <flag name="showTitle" value="0x8"/>
            <flag name="showCustom" value="0x10"/>
            <flag name="disableHome" value="0x20"/>
        </attr>
        <!-- Specifies title text used for navigationMode="normal" -->
        <attr name="title"/>
        <!-- Specifies subtitle text used for navigationMode="normal" -->
        <attr format="string" name="subtitle"/>
        <!-- Specifies a style to use for title text. -->
        <attr format="reference" name="titleTextStyle"/>
        <!-- Specifies a style to use for subtitle text. -->
        <attr format="reference" name="subtitleTextStyle"/>
        <!-- Specifies the drawable used for the application icon. -->
        <attr format="reference" name="icon"/>
        <!-- Specifies the drawable used for the application logo. -->
        <attr format="reference" name="logo"/>
        <!-- Specifies the drawable used for item dividers. -->
        <attr format="reference" name="divider"/>
        <!-- Specifies a background drawable for the action bar. -->
        <attr format="reference" name="background"/>
        <!-- Specifies a background drawable for a second stacked row of the action bar. -->
        <attr format="reference|color" name="backgroundStacked"/>
        <!-- Specifies a background drawable for the bottom component of a split action bar. -->
        <attr format="reference|color" name="backgroundSplit"/>
        <!-- Specifies a layout for custom navigation. Overrides navigationMode. -->
        <attr format="reference" name="customNavigationLayout"/>
        <!-- Specifies a fixed height. -->
        <attr name="height"/>
        <!-- Specifies a layout to use for the "home" section of the action bar. -->
        <attr format="reference" name="homeLayout"/>
        <!-- Specifies a style resource to use for an embedded progress bar. -->
        <attr format="reference" name="progressBarStyle"/>
        <!-- Specifies a style resource to use for an indeterminate progress spinner. -->
        <attr format="reference" name="indeterminateProgressStyle"/>
        <!-- Specifies the horizontal padding on either end for an embedded progress bar. -->
        <attr format="dimension" name="progressBarPadding"/>
        <!-- Up navigation glyph -->
        <attr name="homeAsUpIndicator"/>
        <!-- Specifies padding that should be applied to the left and right sides of
             system-provided items in the bar. -->
        <attr format="dimension" name="itemPadding"/>
        <!-- Set true to hide the action bar on a vertical nested scroll of content. -->
        <attr format="boolean" name="hideOnContentScroll"/>
        <!-- Minimum inset for content views within a bar. Navigation buttons and
             menu views are excepted. Only valid for some themes and configurations. -->
        <attr format="dimension" name="contentInsetStart"/>
        <!-- Minimum inset for content views within a bar. Navigation buttons and
             menu views are excepted. Only valid for some themes and configurations. -->
        <attr format="dimension" name="contentInsetEnd"/>
        <!-- Minimum inset for content views within a bar. Navigation buttons and
             menu views are excepted. Only valid for some themes and configurations. -->
        <attr format="dimension" name="contentInsetLeft"/>
        <!-- Minimum inset for content views within a bar. Navigation buttons and
             menu views are excepted. Only valid for some themes and configurations. -->
        <attr format="dimension" name="contentInsetRight"/>
        <!-- Minimum inset for content views within a bar when a navigation button
             is present, such as the Up button. Only valid for some themes and configurations. -->
        <attr format="dimension" name="contentInsetStartWithNavigation"/>
        <!-- Minimum inset for content views within a bar when actions from a menu
             are present. Only valid for some themes and configurations. -->
        <attr format="dimension" name="contentInsetEndWithActions"/>
        <!-- Elevation for the action bar itself -->
        <attr format="dimension" name="elevation"/>
        <!-- Reference to a theme that should be used to inflate popups
             shown by widgets in the action bar. -->
        <attr format="reference" name="popupTheme"/>
    </declare-styleable>
    <declare-styleable name="ActionBarLayout">
        <attr name="android:layout_gravity"/>
    </declare-styleable>
    <declare-styleable name="ActionMenuItemView">
        <attr name="android:minWidth"/>
    </declare-styleable>
    <declare-styleable name="ActionMenuView">
        <!-- Size of padding on either end of a divider. -->
    </declare-styleable>
    <declare-styleable name="ActionMode">
        <!-- Specifies a style to use for title text. -->
        <attr name="titleTextStyle"/>
        <!-- Specifies a style to use for subtitle text. -->
        <attr name="subtitleTextStyle"/>
        <!-- Specifies a background for the action mode bar. -->
        <attr name="background"/>
        <!-- Specifies a background for the split action mode bar. -->
        <attr name="backgroundSplit"/>
        <!-- Specifies a fixed height for the action mode bar. -->
        <attr name="height"/>
        <!-- Specifies a layout to use for the "close" item at the starting edge. -->
        <attr format="reference" name="closeItemLayout"/>
    </declare-styleable>
    <declare-styleable name="ActivityChooserView">
        <!-- The maximal number of items initially shown in the activity list. -->
        <attr format="string" name="initialActivityCount"/>
        <!-- The drawable to show in the button for expanding the activities overflow popup.
             <strong>Note:</strong> Clients would like to set this drawable
             as a clue about the action the chosen activity will perform. For
             example, if share activity is to be chosen the drawable should
             give a clue that sharing is to be performed.
         -->
        <attr format="reference" name="expandActivityOverflowButtonDrawable"/>
    </declare-styleable>
    <declare-styleable name="AlertDialog">
        <attr name="android:layout"/>
        <attr format="reference" name="buttonPanelSideLayout"/>
        <attr format="reference" name="listLayout"/>
        <attr format="reference" name="multiChoiceItemLayout"/>
        <attr format="reference" name="singleChoiceItemLayout"/>
        <attr format="reference" name="listItemLayout"/>
        <attr format="boolean" name="showTitle"/>
        <attr format="dimension" name="buttonIconDimen"/>
    </declare-styleable>
    <declare-styleable name="AnimatedStateListDrawableCompat">
        <!-- Indicates whether the drawable should be initially visible. -->
        <attr name="android:visible"/>
        <!-- If true, allows the drawable's padding to change based on the
             current state that is selected.  If false, the padding will
             stay the same (based on the maximum padding of all the states).
             Enabling this feature requires that the owner of the drawable
             deal with performing layout when the state changes, which is
             often not supported. -->
        <attr name="android:variablePadding"/>
        <!-- If true, the drawable's reported internal size will remain
             constant as the state changes; the size is the maximum of all
             of the states.  If false, the size will vary based on the
             current state. -->
        <attr name="android:constantSize"/>
        <!-- Enables or disables dithering of the bitmap if the bitmap does not have the
             same pixel configuration as the screen (for instance: a ARGB 8888 bitmap with
             an RGB 565 screen). -->
        <attr name="android:dither"/>
        <!-- Amount of time (in milliseconds) to fade in a new state drawable. -->
        <attr name="android:enterFadeDuration"/>
        <!-- Amount of time (in milliseconds) to fade out an old state drawable. -->
        <attr name="android:exitFadeDuration"/>
        <!-- Indicates if the drawable needs to be mirrored when its layout direction is
             RTL (right-to-left). -->
        <!--<attr name="autoMirrored"/>-->
    </declare-styleable>
    <declare-styleable name="AnimatedStateListDrawableItem">
        <!-- Reference to a drawable resource to use for the frame.  If not
             given, the drawable must be defined by the first child tag. -->
        <attr name="android:drawable"/>
        <!-- Keyframe identifier for use in specifying transitions. -->
        <attr name="android:id"/>
    </declare-styleable>
    <declare-styleable name="AnimatedStateListDrawableTransition">
        <!-- Keyframe identifier for the starting state. -->
        <attr name="android:fromId"/>
        <!-- Keyframe identifier for the ending state. -->
        <attr name="android:toId"/>
        <!-- Reference to a animation drawable resource to use for the frame.  If not
             given, the animation drawable must be defined by the first child tag. -->
        <attr name="android:drawable"/>
        <!-- Whether this transition is reversible. -->
        <attr name="android:reversible"/>
    </declare-styleable>
    <declare-styleable name="AppCompatImageView">
        <attr name="android:src"/>
        <!-- Sets a drawable as the content of this ImageView. Allows the use of vector drawable
             when running on older versions of the platform. -->
        <attr format="reference" name="srcCompat"/>

        <!-- Tint to apply to the image source. -->
        <attr format="color" name="tint"/>

        <!-- Blending mode used to apply the image source tint. -->
        <attr name="tintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="AppCompatSeekBar">
        <attr name="android:thumb"/>
        <!-- Drawable displayed at each progress position on a seekbar. -->
        <attr format="reference" name="tickMark"/>
        <!-- Tint to apply to the tick mark drawable. -->
        <attr format="color" name="tickMarkTint"/>
        <!-- Blending mode used to apply the tick mark tint. -->
        <attr name="tickMarkTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and drawable color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="AppCompatTextHelper">
        <attr name="android:drawableLeft"/>
        <attr name="android:drawableTop"/>
        <attr name="android:drawableRight"/>
        <attr name="android:drawableBottom"/>
        <attr name="android:drawableStart"/>
        <attr name="android:drawableEnd"/>
        <attr name="android:textAppearance"/>
    </declare-styleable>
    <declare-styleable name="AppCompatTextView">
        <!-- Present the text in ALL CAPS. This may use a small-caps form when available. -->
        <attr format="reference|boolean" name="textAllCaps"/>
        <!-- Set the textLocale by a comma-separated language tag string,
             for example "ja-JP,zh-CN". This attribute only takes effect on API 21 and above.
             Before API 24, only the first language tag is used. Starting from API 24,
             the string will be converted into a {@link android.os.LocaleList} and then used by
             {@link android.widget.TextView} -->
        <attr format="string" name="textLocale"/>
        <attr name="android:textAppearance"/>
        <!-- Specify the type of auto-size. Note that this feature is not supported by EditText,
        works only for TextView. -->
        <attr format="enum" name="autoSizeTextType">
            <!-- No auto-sizing (default). -->
            <enum name="none" value="0"/>
            <!-- Uniform horizontal and vertical text size scaling to fit within the
            container. -->
            <enum name="uniform" value="1"/>
        </attr>
        <!-- Specify the auto-size step size if <code>autoSizeTextType</code> is set to
        <code>uniform</code>. The default is 1px. Overwrites
        <code>autoSizePresetSizes</code> if set. -->
        <attr format="dimension" name="autoSizeStepGranularity"/>
        <!-- Resource array of dimensions to be used in conjunction with
        <code>autoSizeTextType</code> set to <code>uniform</code>. Overrides
        <code>autoSizeStepGranularity</code> if set. -->
        <attr format="reference" name="autoSizePresetSizes"/>
        <!-- The minimum text size constraint to be used when auto-sizing text. -->
        <attr format="dimension" name="autoSizeMinTextSize"/>
        <!-- The maximum text size constraint to be used when auto-sizing text. -->
        <attr format="dimension" name="autoSizeMaxTextSize"/>
        <!-- The attribute for the font family. -->
        <attr format="string" name="fontFamily"/>
        <!-- Explicit height between lines of text. If set, this will override the values set
             for lineSpacingExtra and lineSpacingMultiplier. -->
        <attr format="dimension" name="lineHeight"/>
        <!-- Distance from the top of the TextView to the first text baseline. If set, this
             overrides the value set for paddingTop. -->
        <attr format="dimension" name="firstBaselineToTopHeight"/>
        <!-- Distance from the bottom of the TextView to the last text baseline. If set, this
             overrides the value set for paddingBottom. -->
        <attr format="dimension" name="lastBaselineToBottomHeight"/>
        <!-- OpenType font variation settings, available after api 26. -->
        <attr format="string" name="fontVariationSettings"/>
        <!-- Compound drawables allowing the use of vector drawable when running on older versions
             of the platform. -->
        <attr format="reference" name="drawableLeftCompat"/>
        <attr format="reference" name="drawableTopCompat"/>
        <attr format="reference" name="drawableRightCompat"/>
        <attr format="reference" name="drawableBottomCompat"/>
        <attr format="reference" name="drawableStartCompat"/>
        <attr format="reference" name="drawableEndCompat"/>
        <!-- Tint to apply to the compound (left, top, etc.) drawables. -->
        <attr format="color" name="drawableTint"/>
        <!-- Blending mode used to apply the compound (left, top, etc.) drawables tint. -->
        <attr name="drawableTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and drawable color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="AppCompatTheme">

        <!-- ============= -->
        <!-- Window styles -->
        <!-- ============= -->
        <eat-comment/>

        <!-- Flag indicating whether this window should have an Action Bar
             in place of the usual title bar. -->
        <attr format="boolean" name="windowActionBar"/>

        <!-- Flag indicating whether there should be no title on this window. -->
        <attr format="boolean" name="windowNoTitle"/>

        <!-- Flag indicating whether this window's Action Bar should overlay
             application content. Does nothing if the window would not
             have an Action Bar. -->
        <attr format="boolean" name="windowActionBarOverlay"/>

        <!-- Flag indicating whether action modes should overlay window content
             when there is not reserved space for their UI (such as an Action Bar). -->
        <attr format="boolean" name="windowActionModeOverlay"/>

        <!-- A fixed width for the window along the major axis of the screen,
             that is, when in landscape. Can be either an absolute dimension
             or a fraction of the screen size in that dimension. -->
        <attr format="dimension|fraction" name="windowFixedWidthMajor"/>
        <!-- A fixed height for the window along the minor axis of the screen,
             that is, when in landscape. Can be either an absolute dimension
             or a fraction of the screen size in that dimension. -->
        <attr format="dimension|fraction" name="windowFixedHeightMinor"/>

        <!-- A fixed width for the window along the minor axis of the screen,
             that is, when in portrait. Can be either an absolute dimension
             or a fraction of the screen size in that dimension. -->
        <attr format="dimension|fraction" name="windowFixedWidthMinor"/>
        <!-- A fixed height for the window along the major axis of the screen,
             that is, when in portrait. Can be either an absolute dimension
             or a fraction of the screen size in that dimension. -->
        <attr format="dimension|fraction" name="windowFixedHeightMajor"/>

        <!-- The minimum width the window is allowed to be, along the major
             axis of the screen.  That is, when in landscape.  Can be either
             an absolute dimension or a fraction of the screen size in that
             dimension. -->
        <attr format="dimension|fraction" name="windowMinWidthMajor"/>
        <!-- The minimum width the window is allowed to be, along the minor
             axis of the screen.  That is, when in portrait.  Can be either
             an absolute dimension or a fraction of the screen size in that
             dimension. -->
        <attr format="dimension|fraction" name="windowMinWidthMinor"/>

        <attr name="android:windowIsFloating"/>
        <attr name="android:windowAnimationStyle"/>

        <!-- =================== -->
        <!-- Action bar styles   -->
        <!-- =================== -->
        <eat-comment/>
        <!-- Default style for tabs within an action bar -->
        <attr format="reference" name="actionBarTabStyle"/>
        <attr format="reference" name="actionBarTabBarStyle"/>
        <attr format="reference" name="actionBarTabTextStyle"/>
        <attr format="reference" name="actionOverflowButtonStyle"/>
        <attr format="reference" name="actionOverflowMenuStyle"/>
        <!-- Reference to a theme that should be used to inflate popups
             shown by widgets in the action bar. -->
        <attr format="reference" name="actionBarPopupTheme"/>
        <!-- Reference to a style for the Action Bar -->
        <attr format="reference" name="actionBarStyle"/>
        <!-- Reference to a style for the split Action Bar. This style
             controls the split component that holds the menu/action
             buttons. actionBarStyle is still used for the primary
             bar. -->
        <attr format="reference" name="actionBarSplitStyle"/>
        <!-- Reference to a theme that should be used to inflate the
             action bar. This will be inherited by any widget inflated
             into the action bar. -->
        <attr format="reference" name="actionBarTheme"/>
        <!-- Reference to a theme that should be used to inflate widgets
             and layouts destined for the action bar. Most of the time
             this will be a reference to the current theme, but when
             the action bar has a significantly different contrast
             profile than the rest of the activity the difference
             can become important. If this is set to @null the current
             theme will be used.-->
        <attr format="reference" name="actionBarWidgetTheme"/>
        <!-- Size of the Action Bar, including the contextual
             bar used to present Action Modes. -->
        <attr format="dimension" name="actionBarSize">
            <enum name="wrap_content" value="0"/>
        </attr>
        <!-- Custom divider drawable to use for elements in the action bar. -->
        <attr format="reference" name="actionBarDivider"/>
        <!-- Custom item state list drawable background for action bar items. -->
        <attr format="reference" name="actionBarItemBackground"/>
        <!-- TextAppearance style that will be applied to text that
             appears within action menu items. -->
        <attr format="reference" name="actionMenuTextAppearance"/>
        <!-- Color for text that appears within action menu items. -->
        <!-- Color for text that appears within action menu items. -->
        <attr format="color|reference" name="actionMenuTextColor"/>


        <!-- =================== -->
        <!-- Action mode styles  -->
        <!-- =================== -->
        <eat-comment/>
        <attr format="reference" name="actionModeStyle"/>
        <attr format="reference" name="actionModeCloseButtonStyle"/>
        <!-- Background drawable to use for action mode UI -->
        <attr format="reference" name="actionModeBackground"/>
        <!-- Background drawable to use for action mode UI in the lower split bar -->
        <attr format="reference" name="actionModeSplitBackground"/>
        <!-- Drawable to use for the close action mode button -->
        <attr format="reference" name="actionModeCloseDrawable"/>
        <!-- Drawable to use for the Cut action button in Contextual Action Bar -->
        <attr format="reference" name="actionModeCutDrawable"/>
        <!-- Drawable to use for the Copy action button in Contextual Action Bar -->
        <attr format="reference" name="actionModeCopyDrawable"/>
        <!-- Drawable to use for the Paste action button in Contextual Action Bar -->
        <attr format="reference" name="actionModePasteDrawable"/>
        <!-- Drawable to use for the Select all action button in Contextual Action Bar -->
        <attr format="reference" name="actionModeSelectAllDrawable"/>
        <!-- Drawable to use for the Share action button in WebView selection action modes -->
        <attr format="reference" name="actionModeShareDrawable"/>
        <!-- Drawable to use for the Find action button in WebView selection action modes -->
        <attr format="reference" name="actionModeFindDrawable"/>
        <!-- Drawable to use for the Web Search action button in WebView selection action modes -->
        <attr format="reference" name="actionModeWebSearchDrawable"/>

        <!-- PopupWindow style to use for action modes when showing as a window overlay. -->
        <attr format="reference" name="actionModePopupWindowStyle"/>


        <!-- =================== -->
        <!-- Text styles -->
        <!-- =================== -->
        <eat-comment/>
        <!-- Text color, typeface, size, and style for the text inside of a popup menu. -->
        <attr format="reference" name="textAppearanceLargePopupMenu"/>
        <!-- Text color, typeface, size, and style for small text inside of a popup menu. -->
        <attr format="reference" name="textAppearanceSmallPopupMenu"/>
        <!-- Text color, typeface, size, and style for header text inside of a popup menu. -->
        <attr format="reference" name="textAppearancePopupMenuHeader"/>


        <!-- =================== -->
        <!-- Dialog styles -->
        <!-- =================== -->
        <eat-comment/>

        <!-- Theme to use for dialogs spawned from this theme. -->
        <attr format="reference" name="dialogTheme"/>
        <!-- Preferred padding for dialog content. -->
        <attr format="dimension" name="dialogPreferredPadding"/>
        <!-- The list divider used in alert dialogs. -->
        <attr format="reference" name="listDividerAlertDialog"/>
        <!-- Preferred corner radius of dialogs. -->
        <attr format="dimension" name="dialogCornerRadius"/>

        <!-- =================== -->
        <!-- Other widget styles -->
        <!-- =================== -->
        <eat-comment/>

        <!-- Default ActionBar dropdown style. -->
        <attr format="reference" name="actionDropDownStyle"/>
        <!-- The preferred item height for dropdown lists. -->
        <attr format="dimension" name="dropdownListPreferredItemHeight"/>
        <!-- Default Spinner style. -->
        <attr format="reference" name="spinnerDropDownItemStyle"/>
        <!-- Specifies a drawable to use for the 'home as up' indicator. -->
        <attr format="reference" name="homeAsUpIndicator"/>

        <!-- Default action button style. -->
        <attr format="reference" name="actionButtonStyle"/>

        <!-- Style for button bars -->
        <attr format="reference" name="buttonBarStyle"/>
        <!-- Style for buttons within button bars -->
        <attr format="reference" name="buttonBarButtonStyle"/>
        <!-- A style that may be applied to buttons or other selectable items
             that should react to pressed and focus states, but that do not
             have a clear visual border along the edges. -->
        <attr format="reference" name="selectableItemBackground"/>
        <!-- Background drawable for borderless standalone items that need focus/pressed states. -->
        <attr format="reference" name="selectableItemBackgroundBorderless"/>
        <!-- Style for buttons without an explicit border, often used in groups. -->
        <attr format="reference" name="borderlessButtonStyle"/>
        <!-- A drawable that may be used as a vertical divider between visual elements. -->
        <attr format="reference" name="dividerVertical"/>
        <!-- A drawable that may be used as a horizontal divider between visual elements. -->
        <attr format="reference" name="dividerHorizontal"/>
        <!-- Default ActivityChooserView style. -->
        <attr format="reference" name="activityChooserViewStyle"/>

        <!-- Default Toolbar style. -->
        <attr format="reference" name="toolbarStyle"/>
        <!-- Default Toolar NavigationButtonStyle -->
        <attr format="reference" name="toolbarNavigationButtonStyle"/>

        <!-- Default PopupMenu style. -->
        <attr format="reference" name="popupMenuStyle"/>
        <!-- Default PopupWindow style. -->
        <attr format="reference" name="popupWindowStyle"/>

        <!-- EditText text foreground color. -->
        <attr format="reference|color" name="editTextColor"/>
        <!-- EditText background drawable. -->
        <attr format="reference" name="editTextBackground"/>

        <!-- ImageButton background drawable. -->
        <attr format="reference" name="imageButtonStyle"/>

        <!-- ============================ -->
        <!-- SearchView styles and assets -->
        <!-- ============================ -->
        <eat-comment/>
        <!-- Text color, typeface, size, and style for system search result title. Defaults to primary inverse text color. -->
        <attr format="reference" name="textAppearanceSearchResultTitle"/>
        <!-- Text color, typeface, size, and style for system search result subtitle. Defaults to primary inverse text color. -->
        <attr format="reference" name="textAppearanceSearchResultSubtitle"/>
        <!-- Text color for urls in search suggestions, used by things like global search -->
        <attr format="reference|color" name="textColorSearchUrl"/>
        <!-- Style for the search query widget. -->
        <attr format="reference" name="searchViewStyle"/>

        <!-- =========== -->
        <!-- List styles -->
        <!-- =========== -->
        <eat-comment/>

        <!-- The preferred list item height. -->
        <attr format="dimension" name="listPreferredItemHeight"/>
        <!-- A smaller, sleeker list item height. -->
        <attr format="dimension" name="listPreferredItemHeightSmall"/>
        <!-- A larger, more robust list item height. -->
        <attr format="dimension" name="listPreferredItemHeightLarge"/>

        <!-- The preferred padding along the left edge of list items. -->
        <attr format="dimension" name="listPreferredItemPaddingLeft"/>
        <!-- The preferred padding along the right edge of list items. -->
        <attr format="dimension" name="listPreferredItemPaddingRight"/>

        <!-- The preferred padding along the start edge of list items. -->
        <attr format="dimension" name="listPreferredItemPaddingStart"/>
        <!-- The preferred padding along the end edge of list items. -->
        <attr format="dimension" name="listPreferredItemPaddingEnd"/>

        <!-- ListPopupWindow compatibility -->
        <attr format="reference" name="dropDownListViewStyle"/>
        <attr format="reference" name="listPopupWindowStyle"/>

        <!-- The preferred TextAppearance for the primary text of list items. -->
        <attr format="reference" name="textAppearanceListItem"/>
        <!-- The preferred TextAppearance for the secondary text of list items. -->
        <attr format="reference" name="textAppearanceListItemSecondary"/>
        <!-- The preferred TextAppearance for the primary text of small list items. -->
        <attr format="reference" name="textAppearanceListItemSmall"/>

        <!-- ============ -->
        <!-- Panel styles -->
        <!-- ============ -->
        <eat-comment/>

        <!-- The background of a panel when it is inset from the left and right edges of the screen. -->
        <attr format="reference" name="panelBackground"/>
        <!-- Default Panel Menu width. -->
        <attr format="dimension" name="panelMenuListWidth"/>
        <!-- Default Panel Menu style. -->
        <attr format="reference" name="panelMenuListTheme"/>
        <!-- Drawable used as a background for selected list items. -->
        <attr format="reference" name="listChoiceBackgroundIndicator"/>

        <!-- ============= -->
        <!-- Color palette -->
        <!-- ============= -->
        <eat-comment/>

        <!-- The primary branding color for the app. By default, this is the color applied to the
             action bar background. -->
        <attr format="color" name="colorPrimary"/>

        <!-- Dark variant of the primary branding color. By default, this is the color applied to
             the status bar (via statusBarColor) and navigation bar (via navigationBarColor). -->
        <attr format="color" name="colorPrimaryDark"/>

        <!-- Bright complement to the primary branding color. By default, this is the color applied
             to framework controls (via colorControlActivated). -->
        <attr format="color" name="colorAccent"/>

        <!-- The color applied to framework controls in their normal state. -->
        <attr format="color" name="colorControlNormal"/>

        <!-- The color applied to framework controls in their activated (ex. checked) state. -->
        <attr format="color" name="colorControlActivated"/>

        <!-- The color applied to framework control highlights (ex. ripples, list selectors). -->
        <attr format="color" name="colorControlHighlight"/>

        <!-- The color applied to framework buttons in their normal state. -->
        <attr format="color" name="colorButtonNormal"/>

        <!-- The color applied to framework switch thumbs in their normal state. -->
        <attr format="color" name="colorSwitchThumbNormal"/>

        <!-- The background used by framework controls. -->
        <attr format="reference" name="controlBackground"/>

        <!-- Default color of background imagery for floating components, ex. dialogs, popups, and cards. -->
        <attr format="color" name="colorBackgroundFloating"/>

        <!-- ============ -->
        <!-- Alert Dialog styles -->
        <!-- ============ -->
        <eat-comment/>
        <attr format="reference" name="alertDialogStyle"/>
        <attr format="reference" name="alertDialogButtonGroupStyle"/>
        <attr format="boolean" name="alertDialogCenterButtons"/>
        <!-- Theme to use for alert dialogs spawned from this theme. -->
        <attr format="reference" name="alertDialogTheme"/>

        <!-- Color of list item text in alert dialogs. -->
        <attr format="reference|color" name="textColorAlertDialogListItem"/>

        <!-- Style for the "positive" buttons within button bars -->
        <attr format="reference" name="buttonBarPositiveButtonStyle"/>

        <!-- Style for the "negative" buttons within button bars -->
        <attr format="reference" name="buttonBarNegativeButtonStyle"/>

        <!-- Style for the "neutral" buttons within button bars -->
        <attr format="reference" name="buttonBarNeutralButtonStyle"/>

        <!-- ===================== -->
        <!-- Default widget styles -->
        <!-- ===================== -->
        <eat-comment/>

        <!-- Default AutoCompleteTextView style. -->
        <attr format="reference" name="autoCompleteTextViewStyle"/>
        <!-- Normal Button style. -->
        <attr format="reference" name="buttonStyle"/>
        <!-- Small Button style. -->
        <attr format="reference" name="buttonStyleSmall"/>
        <!-- Default Checkbox style. -->
        <attr format="reference" name="checkboxStyle"/>
        <!-- Default CheckedTextView style. -->
        <attr format="reference" name="checkedTextViewStyle"/>
        <!-- Default EditText style. -->
        <attr format="reference" name="editTextStyle"/>
        <!-- Default RadioButton style. -->
        <attr format="reference" name="radioButtonStyle"/>
        <!-- Default RatingBar style. -->
        <attr format="reference" name="ratingBarStyle"/>
        <!-- Indicator RatingBar style. -->
        <attr format="reference" name="ratingBarStyleIndicator"/>
        <!-- Small indicator RatingBar style. -->
        <attr format="reference" name="ratingBarStyleSmall"/>
        <!-- Default SeekBar style. -->
        <attr format="reference" name="seekBarStyle"/>
        <!-- Default Spinner style. -->
        <attr format="reference" name="spinnerStyle"/>
        <!-- Default style for the Switch widget. -->
        <attr format="reference" name="switchStyle"/>

        <!-- Default menu-style ListView style. -->
        <attr format="reference" name="listMenuViewStyle"/>

        <!-- ===================== -->
        <!-- Tooltip styles -->
        <!-- ===================== -->
        <eat-comment/>

        <!-- Background to use for tooltips -->
        <attr format="reference" name="tooltipFrameBackground"/>
        <!-- Foreground color to use for tooltips -->
        <attr format="reference|color" name="tooltipForegroundColor"/>

        <!-- Color used for error states and things that need to be drawn to
             the user's attention. -->
        <attr format="reference|color" name="colorError"/>

        <attr format="string" name="viewInflaterClass"/>

        <!-- ===================== -->
        <!-- Animated list choice indicators -->
        <!-- ===================== -->
        <eat-comment/>

        <!-- Animated Drawable to use for single choice indicators. -->
        <attr format="reference" name="listChoiceIndicatorMultipleAnimated"/>
        <!-- Animated Drawable to use for multiple choice indicators. -->
        <attr format="reference" name="listChoiceIndicatorSingleAnimated"/>

    </declare-styleable>
    <declare-styleable name="ButtonBarLayout">
        <!-- Whether to automatically stack the buttons when there is not
             enough space to lay them out side-by-side. -->
        <attr format="boolean" name="allowStacking"/>
    </declare-styleable>
    <declare-styleable name="CircleProgressImageView">
        <attr format="color|reference" name="progress_color"/>
        <attr format="color|reference" name="back_color"/>
        <attr format="dimension|reference" name="progress_width"/>
    </declare-styleable>
    <declare-styleable name="ColorStateListItem">
        <!-- Base color for this state. -->
        <attr name="android:color"/>
        <!-- Alpha multiplier applied to the base color. -->
        <attr format="float" name="alpha"/>
        <attr name="android:alpha"/>
    </declare-styleable>
    <declare-styleable name="CompoundButton">
        <attr name="android:button"/>
        <!-- Compat attr to load backported drawable types -->
        <attr format="reference" name="buttonCompat"/>
        <!-- Tint to apply to the button drawable. -->
        <attr format="color" name="buttonTint"/>

        <!-- Blending mode used to apply the button tint. -->
        <attr name="buttonTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="ConstraintLayout_Layout"><attr name="android:orientation"/><attr name="android:minWidth"/><attr name="android:minHeight"/><attr name="android:maxWidth"/><attr name="android:maxHeight"/><attr name="layout_optimizationLevel"/><attr name="constraintSet"/><attr name="barrierDirection"/><attr name="barrierAllowsGoneWidgets"/><attr name="constraint_referenced_ids"/><attr name="chainUseRtl"/><attr name="layout_constraintCircle"/><attr name="layout_constraintCircleRadius"/><attr name="layout_constraintCircleAngle"/><attr name="layout_constraintGuide_begin"/><attr name="layout_constraintGuide_end"/><attr name="layout_constraintGuide_percent"/><attr name="layout_constraintLeft_toLeftOf"/><attr name="layout_constraintLeft_toRightOf"/><attr name="layout_constraintRight_toLeftOf"/><attr name="layout_constraintRight_toRightOf"/><attr name="layout_constraintTop_toTopOf"/><attr name="layout_constraintTop_toBottomOf"/><attr name="layout_constraintBottom_toTopOf"/><attr name="layout_constraintBottom_toBottomOf"/><attr name="layout_constraintBaseline_toBaselineOf"/><attr name="layout_constraintStart_toEndOf"/><attr name="layout_constraintStart_toStartOf"/><attr name="layout_constraintEnd_toStartOf"/><attr name="layout_constraintEnd_toEndOf"/><attr name="layout_goneMarginLeft"/><attr name="layout_goneMarginTop"/><attr name="layout_goneMarginRight"/><attr name="layout_goneMarginBottom"/><attr name="layout_goneMarginStart"/><attr name="layout_goneMarginEnd"/><attr name="layout_constrainedWidth"/><attr name="layout_constrainedHeight"/><attr name="layout_constraintHorizontal_bias"/><attr name="layout_constraintVertical_bias"/><attr name="layout_constraintWidth_default"/><attr name="layout_constraintHeight_default"/><attr name="layout_constraintWidth_min"/><attr name="layout_constraintWidth_max"/><attr name="layout_constraintWidth_percent"/><attr name="layout_constraintHeight_min"/><attr name="layout_constraintHeight_max"/><attr name="layout_constraintHeight_percent"/><attr name="layout_constraintLeft_creator"/><attr name="layout_constraintTop_creator"/><attr name="layout_constraintRight_creator"/><attr name="layout_constraintBottom_creator"/><attr name="layout_constraintBaseline_creator"/><attr name="layout_constraintDimensionRatio"/><attr name="layout_constraintHorizontal_weight"/><attr name="layout_constraintVertical_weight"/><attr name="layout_constraintHorizontal_chainStyle"/><attr name="layout_constraintVertical_chainStyle"/><attr name="layout_editor_absoluteX"/><attr name="layout_editor_absoluteY"/></declare-styleable>
    <declare-styleable name="ConstraintLayout_placeholder"><attr name="emptyVisibility"/><attr name="content"/></declare-styleable>
    <declare-styleable name="ConstraintSet"><attr name="android:orientation"/><attr name="android:id"/><attr name="android:visibility"/><attr name="android:alpha"/><attr name="android:elevation"/><attr name="android:rotation"/><attr name="android:rotationX"/><attr name="android:rotationY"/><attr name="android:scaleX"/><attr name="android:scaleY"/><attr name="android:transformPivotX"/><attr name="android:transformPivotY"/><attr name="android:translationX"/><attr name="android:translationY"/><attr name="android:translationZ"/><attr name="android:layout_width"/><attr name="android:layout_height"/><attr name="android:layout_marginStart"/><attr name="android:layout_marginBottom"/><attr name="android:layout_marginTop"/><attr name="android:layout_marginEnd"/><attr name="android:layout_marginLeft"/><attr name="android:layout_marginRight"/><attr name="layout_constraintCircle"/><attr name="layout_constraintCircleRadius"/><attr name="layout_constraintCircleAngle"/><attr name="layout_constraintGuide_begin"/><attr name="layout_constraintGuide_end"/><attr name="layout_constraintGuide_percent"/><attr name="layout_constraintLeft_toLeftOf"/><attr name="layout_constraintLeft_toRightOf"/><attr name="layout_constraintRight_toLeftOf"/><attr name="layout_constraintRight_toRightOf"/><attr name="layout_constraintTop_toTopOf"/><attr name="layout_constraintTop_toBottomOf"/><attr name="layout_constraintBottom_toTopOf"/><attr name="layout_constraintBottom_toBottomOf"/><attr name="layout_constraintBaseline_toBaselineOf"/><attr name="layout_constraintStart_toEndOf"/><attr name="layout_constraintStart_toStartOf"/><attr name="layout_constraintEnd_toStartOf"/><attr name="layout_constraintEnd_toEndOf"/><attr name="layout_goneMarginLeft"/><attr name="layout_goneMarginTop"/><attr name="layout_goneMarginRight"/><attr name="layout_goneMarginBottom"/><attr name="layout_goneMarginStart"/><attr name="layout_goneMarginEnd"/><attr name="layout_constrainedWidth"/><attr name="layout_constrainedHeight"/><attr name="layout_constraintHorizontal_bias"/><attr name="layout_constraintVertical_bias"/><attr name="layout_constraintWidth_default"/><attr name="layout_constraintHeight_default"/><attr name="layout_constraintWidth_min"/><attr name="layout_constraintWidth_max"/><attr name="layout_constraintWidth_percent"/><attr name="layout_constraintHeight_min"/><attr name="layout_constraintHeight_max"/><attr name="layout_constraintHeight_percent"/><attr name="layout_constraintLeft_creator"/><attr name="layout_constraintTop_creator"/><attr name="layout_constraintRight_creator"/><attr name="layout_constraintBottom_creator"/><attr name="layout_constraintBaseline_creator"/><attr name="layout_constraintDimensionRatio"/><attr name="layout_constraintHorizontal_weight"/><attr name="layout_constraintVertical_weight"/><attr name="layout_constraintHorizontal_chainStyle"/><attr name="layout_constraintVertical_chainStyle"/><attr name="layout_editor_absoluteX"/><attr name="layout_editor_absoluteY"/><attr name="barrierDirection"/><attr name="constraint_referenced_ids"/><attr name="android:maxHeight"/><attr name="android:maxWidth"/><attr name="android:minHeight"/><attr name="android:minWidth"/><attr name="barrierAllowsGoneWidgets"/><attr name="chainUseRtl"/></declare-styleable>
    <declare-styleable name="CoordinatorLayout">
        <!-- A reference to an array of integers representing the
             locations of horizontal keylines in dp from the starting edge.
             Child views can refer to these keylines for alignment using
             layout_keyline="index" where index is a 0-based index into
             this array. -->
        <attr format="reference" name="keylines"/>
        <!-- Drawable to display behind the status bar when the view is set to draw behind it. -->
        <attr format="color|reference" name="statusBarBackground"/>
    </declare-styleable>
    <declare-styleable name="CoordinatorLayout_Layout">
        <attr name="android:layout_gravity"/>
        <!-- The class name of a Behavior class defining special runtime behavior
             for this child view. -->
        <attr format="string" name="layout_behavior"/>
        <!-- The id of an anchor view that this view should position relative to. -->
        <attr format="reference" name="layout_anchor"/>
        <!-- The index of a keyline this view should position relative to.
             android:layout_gravity will affect how the view aligns to the
             specified keyline. -->
        <attr format="integer" name="layout_keyline"/>

        <!-- Specifies how an object should position relative to an anchor, on both the X and Y axes,
             within its parent's bounds.  -->
        <attr name="layout_anchorGravity">
            <!-- Push object to the top of its container, not changing its size. -->
            <flag name="top" value="0x30"/>
            <!-- Push object to the bottom of its container, not changing its size. -->
            <flag name="bottom" value="0x50"/>
            <!-- Push object to the left of its container, not changing its size. -->
            <flag name="left" value="0x03"/>
            <!-- Push object to the right of its container, not changing its size. -->
            <flag name="right" value="0x05"/>
            <!-- Place object in the vertical center of its container, not changing its size. -->
            <flag name="center_vertical" value="0x10"/>
            <!-- Grow the vertical size of the object if needed so it completely fills its container. -->
            <flag name="fill_vertical" value="0x70"/>
            <!-- Place object in the horizontal center of its container, not changing its size. -->
            <flag name="center_horizontal" value="0x01"/>
            <!-- Grow the horizontal size of the object if needed so it completely fills its container. -->
            <flag name="fill_horizontal" value="0x07"/>
            <!-- Place the object in the center of its container in both the vertical and horizontal axis, not changing its size. -->
            <flag name="center" value="0x11"/>
            <!-- Grow the horizontal and vertical size of the object if needed so it completely fills its container. -->
            <flag name="fill" value="0x77"/>
            <!-- Additional option that can be set to have the top and/or bottom edges of
                 the child clipped to its container's bounds.
                 The clip will be based on the vertical gravity: a top gravity will clip the bottom
                 edge, a bottom gravity will clip the top edge, and neither will clip both edges. -->
            <flag name="clip_vertical" value="0x80"/>
            <!-- Additional option that can be set to have the left and/or right edges of
                 the child clipped to its container's bounds.
                 The clip will be based on the horizontal gravity: a left gravity will clip the right
                 edge, a right gravity will clip the left edge, and neither will clip both edges. -->
            <flag name="clip_horizontal" value="0x08"/>
            <!-- Push object to the beginning of its container, not changing its size. -->
            <flag name="start" value="0x00800003"/>
            <!-- Push object to the end of its container, not changing its size. -->
            <flag name="end" value="0x00800005"/>
        </attr>

        <!-- Specifies how this view insets the CoordinatorLayout and make some other views
             dodge it. -->
        <attr format="enum" name="layout_insetEdge">
            <!-- Don't inset. -->
            <enum name="none" value="0x0"/>
            <!-- Inset the top edge. -->
            <enum name="top" value="0x30"/>
            <!-- Inset the bottom edge. -->
            <enum name="bottom" value="0x50"/>
            <!-- Inset the left edge. -->
            <enum name="left" value="0x03"/>
            <!-- Inset the right edge. -->
            <enum name="right" value="0x05"/>
            <!-- Inset the start edge. -->
            <enum name="start" value="0x00800003"/>
            <!-- Inset the end edge. -->
            <enum name="end" value="0x00800005"/>
        </attr>
        <!-- Specifies how this view dodges the inset edges of the CoordinatorLayout. -->
        <attr name="layout_dodgeInsetEdges">
            <!-- Don't dodge any edges -->
            <flag name="none" value="0x0"/>
            <!-- Dodge the top inset edge. -->
            <flag name="top" value="0x30"/>
            <!-- Dodge the bottom inset edge. -->
            <flag name="bottom" value="0x50"/>
            <!-- Dodge the left inset edge. -->
            <flag name="left" value="0x03"/>
            <!-- Dodge the right inset edge. -->
            <flag name="right" value="0x05"/>
            <!-- Dodge the start inset edge. -->
            <flag name="start" value="0x00800003"/>
            <!-- Dodge the end inset edge. -->
            <flag name="end" value="0x00800005"/>
            <!-- Dodge all the inset edges. -->
            <flag name="all" value="0x77"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="DrawerArrowToggle">
        <!-- The drawing color for the bars -->
        <attr format="color" name="color"/>
        <!-- Whether bars should rotate or not during transition -->
        <attr format="boolean" name="spinBars"/>
        <!-- The total size of the drawable -->
        <attr format="dimension" name="drawableSize"/>
        <!-- The max gap between the bars when they are parallel to each other -->
        <attr format="dimension" name="gapBetweenBars"/>
        <!-- The length of the arrow head when formed to make an arrow -->
        <attr format="dimension" name="arrowHeadLength"/>
        <!-- The length of the shaft when formed to make an arrow -->
        <attr format="dimension" name="arrowShaftLength"/>
        <!-- The length of the bars when they are parallel to each other -->
        <attr format="dimension" name="barLength"/>
        <!-- The thickness (stroke size) for the bar paint -->
        <attr format="dimension" name="thickness"/>
    </declare-styleable>
    <declare-styleable name="FontFamily">
        <!-- The authority of the Font Provider to be used for the request. -->
        <attr format="string" name="fontProviderAuthority"/>
        <!-- The package for the Font Provider to be used for the request. This is used to verify
        the identity of the provider. -->
        <attr format="string" name="fontProviderPackage"/>
        <!-- The query to be sent over to the provider. Refer to your font provider's documentation
        on the format of this string. -->
        <attr format="string" name="fontProviderQuery"/>
        <!-- The sets of hashes for the certificates the provider should be signed with. This is
        used to verify the identity of the provider, and is only required if the provider is not
        part of the system image. This value may point to one list or a list of lists, where each
        individual list represents one collection of signature hashes. Refer to your font provider's
        documentation for these values. -->
        <attr format="reference" name="fontProviderCerts"/>
        <!-- The strategy to be used when fetching font data from a font provider in XML layouts.
        This attribute is ignored when the resource is loaded from code, as it is equivalent to the
        choice of API between {@link
    androidx.core.content.res.ResourcesCompat#getFont(Context, int)} (blocking) and
        {@link
    androidx.core.content.res.ResourcesCompat#getFont(Context, int, FontCallback, Handler)}
        (async). -->
        <attr name="fontProviderFetchStrategy">
            <!-- The blocking font fetch works as follows.
              First, check the local cache, then if the requested font is not cached, request the
              font from the provider and wait until it is finished.  You can change the length of
              the timeout by modifying fontProviderFetchTimeout.  If the timeout happens, the
              default typeface will be used instead. -->
            <enum name="blocking" value="0"/>
            <!-- The async font fetch works as follows.
              First, check the local cache, then if the requeted font is not cached, trigger a
              request the font and continue with layout inflation. Once the font fetch succeeds, the
              target text view will be refreshed with the downloaded font data. The
              fontProviderFetchTimeout will be ignored if async loading is specified. -->
            <enum name="async" value="1"/>
        </attr>
        <!-- The length of the timeout during fetching. -->
        <attr format="integer" name="fontProviderFetchTimeout">
            <!-- A special value for the timeout. In this case, the blocking font fetching will not
              timeout and wait until a reply is received from the font provider. -->
            <enum name="forever" value="-1"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="FontFamilyFont">
        <!-- The style of the given font file. This will be used when the font is being loaded into
         the font stack and will override any style information in the font's header tables. If
         unspecified, the value in the font's header tables will be used. -->
        <attr name="fontStyle">
            <enum name="normal" value="0"/>
            <enum name="italic" value="1"/>
        </attr>
        <!-- The reference to the font file to be used. This should be a file in the res/font folder
         and should therefore have an R reference value. E.g. @font/myfont -->
        <attr format="reference" name="font"/>
        <!-- The weight of the given font file. This will be used when the font is being loaded into
         the font stack and will override any weight information in the font's header tables. Must
         be a positive number, a multiple of 100, and between 100 and 900, inclusive. The most
         common values are 400 for regular weight and 700 for bold weight. If unspecified, the value
         in the font's header tables will be used. -->
        <attr format="integer" name="fontWeight"/>
        <!-- The variation settings to be applied to the font. The string should be in the following
         format: "'tag1' value1, 'tag2' value2, ...". If the default variation settings should be
         used, or the font used does not support variation settings, this attribute needs not be
         specified. -->
        <attr format="string" name="fontVariationSettings"/>
        <!-- The index of the font in the tcc font file. If the font file referenced is not in the
        tcc format, this attribute needs not be specified. -->
        <attr format="integer" name="ttcIndex"/>
        <!-- References to the framework attrs -->
        <attr name="android:fontStyle"/>
        <attr name="android:font"/>
        <attr name="android:fontWeight"/>
        <attr name="android:fontVariationSettings"/>
        <attr name="android:ttcIndex"/>
    </declare-styleable>
    <declare-styleable name="Fragment">
        <attr name="android:name"/>
        <attr name="android:id"/>
        <attr name="android:tag"/>
    </declare-styleable>
    <declare-styleable name="FragmentContainerView">
        <attr name="android:name"/>
        <attr name="android:tag"/>
    </declare-styleable>
    <declare-styleable name="GradientColor">
        <!-- Start color of the gradient. -->
        <attr name="android:startColor"/>
        <!-- Optional center color. -->
        <attr name="android:centerColor"/>
        <!-- End color of the gradient. -->
        <attr name="android:endColor"/>
        <!-- Type of gradient. The default type is linear. -->
        <attr name="android:type"/>

        <!-- Only applied to RadialGradient-->
        <!-- Radius of the gradient, used only with radial gradient. -->
        <attr name="android:gradientRadius"/>

        <!-- Only applied to SweepGradient / RadialGradient-->
        <!-- X coordinate of the center of the gradient within the path. -->
        <attr name="android:centerX"/>
        <!-- Y coordinate of the center of the gradient within the path. -->
        <attr name="android:centerY"/>

        <!-- LinearGradient specific -->
        <!-- X coordinate of the start point origin of the gradient.
             Defined in same coordinates as the path itself -->
        <attr name="android:startX"/>
        <!-- Y coordinate of the start point of the gradient within the shape.
             Defined in same coordinates as the path itself -->
        <attr name="android:startY"/>
        <!-- X coordinate of the end point origin of the gradient.
             Defined in same coordinates as the path itself -->
        <attr name="android:endX"/>
        <!-- Y coordinate of the end point of the gradient within the shape.
             Defined in same coordinates as the path itself -->
        <attr name="android:endY"/>

        <!-- Defines the tile mode of the gradient. SweepGradient doesn't support tiling. -->
        <attr name="android:tileMode"/>
    </declare-styleable>
    <declare-styleable name="GradientColorItem">
        <!-- The offset (or ratio) of this current color item inside the gradient.
             The value is only meaningful when it is between 0 and 1. -->
        <attr name="android:offset"/>
        <!-- The current color for the offset inside the gradient. -->
        <attr name="android:color"/>
    </declare-styleable>
    <declare-styleable name="LinearConstraintLayout"><attr name="android:orientation"/></declare-styleable>
    <declare-styleable name="LinearLayoutCompat">
        <!-- Should the layout be a column or a row?  Use "horizontal"
             for a row, "vertical" for a column.  The default is
             horizontal. -->
        <attr name="android:orientation"/>
        <attr name="android:gravity"/>
        <!-- When set to false, prevents the layout from aligning its children's
             baselines. This attribute is particularly useful when the children
             use different values for gravity. The default value is true. -->
        <attr name="android:baselineAligned"/>
        <!-- When a linear layout is part of another layout that is baseline
          aligned, it can specify which of its children to baseline align to
          (that is, which child TextView).-->
        <attr name="android:baselineAlignedChildIndex"/>
        <!-- Defines the maximum weight sum. If unspecified, the sum is computed
             by adding the layout_weight of all of the children. This can be
             used for instance to give a single child 50% of the total available
             space by giving it a layout_weight of 0.5 and setting the weightSum
             to 1.0. -->
        <attr name="android:weightSum"/>
        <!-- When set to true, all children with a weight will be considered having
             the minimum size of the largest child. If false, all children are
             measured normally. -->
        <attr format="boolean" name="measureWithLargestChild"/>
        <!-- Drawable to use as a vertical divider between buttons. -->
        <attr name="divider"/>
        <!-- Setting for which dividers to show. -->
        <attr name="showDividers">
            <flag name="none" value="0"/>
            <flag name="beginning" value="1"/>
            <flag name="middle" value="2"/>
            <flag name="end" value="4"/>
        </attr>
        <!-- Size of padding on either end of a divider. -->
        <attr format="dimension" name="dividerPadding"/>
    </declare-styleable>
    <declare-styleable name="LinearLayoutCompat_Layout">
        <attr name="android:layout_width"/>
        <attr name="android:layout_height"/>
        <attr name="android:layout_weight"/>
        <attr name="android:layout_gravity"/>
    </declare-styleable>
    <declare-styleable name="ListPopupWindow">
        <!-- Amount of pixels by which the drop down should be offset vertically. -->
        <attr name="android:dropDownVerticalOffset"/>
        <!-- Amount of pixels by which the drop down should be offset horizontally. -->
        <attr name="android:dropDownHorizontalOffset"/>
    </declare-styleable>
    <declare-styleable name="MenuGroup">

        <!-- The ID of the group. -->
        <attr name="android:id"/>

        <!-- The category applied to all items within this group.
             (This will be or'ed with the orderInCategory attribute.) -->
        <attr name="android:menuCategory"/>

        <!-- The order within the category applied to all items within this group.
             (This will be or'ed with the category attribute.) -->
        <attr name="android:orderInCategory"/>

        <!-- Whether the items are capable of displaying a check mark. -->
        <attr name="android:checkableBehavior"/>

        <!-- Whether the items are shown/visible. -->
        <attr name="android:visible"/>

        <!-- Whether the items are enabled. -->
        <attr name="android:enabled"/>

    </declare-styleable>
    <declare-styleable name="MenuItem">

        <!-- The ID of the item. -->
        <attr name="android:id"/>

        <!-- The category applied to the item.
             (This will be or'ed with the orderInCategory attribute.) -->
        <attr name="android:menuCategory"/>

        <!-- The order within the category applied to the item.
             (This will be or'ed with the category attribute.) -->
        <attr name="android:orderInCategory"/>

        <!-- The title associated with the item. -->
        <attr name="android:title"/>

        <!-- The condensed title associated with the item.  This is used in situations where the
             normal title may be too long to be displayed. -->
        <attr name="android:titleCondensed"/>

        <!-- The icon associated with this item.  This icon will not always be shown, so
             the title should be sufficient in describing this item. -->
        <attr name="android:icon"/>

        <!-- The alphabetic shortcut key.  This is the shortcut when using a keyboard
             with alphabetic keys. -->
        <attr name="android:alphabeticShortcut"/>

        <!-- The alphabetic modifier key. This is the modifier when using a keyboard
            with alphabetic keys. The values should be kept in sync with KeyEvent -->
        <attr name="alphabeticModifiers">
            <flag name="META" value="0x10000"/>
            <flag name="CTRL" value="0x1000"/>
            <flag name="ALT" value="0x02"/>
            <flag name="SHIFT" value="0x1"/>
            <flag name="SYM" value="0x4"/>
            <flag name="FUNCTION" value="0x8"/>
        </attr>

        <!-- The numeric shortcut key.  This is the shortcut when using a numeric (e.g., 12-key)
             keyboard. -->
        <attr name="android:numericShortcut"/>

        <!-- The numeric modifier key. This is the modifier when using a numeric (e.g., 12-key)
            keyboard. The values should be kept in sync with KeyEvent -->
        <attr name="numericModifiers">
            <flag name="META" value="0x10000"/>
            <flag name="CTRL" value="0x1000"/>
            <flag name="ALT" value="0x02"/>
            <flag name="SHIFT" value="0x1"/>
            <flag name="SYM" value="0x4"/>
            <flag name="FUNCTION" value="0x8"/>
        </attr>

        <!-- Whether the item is capable of displaying a check mark. -->
        <attr name="android:checkable"/>

        <!-- Whether the item is checked.  Note that you must first have enabled checking with
             the checkable attribute or else the check mark will not appear. -->
        <attr name="android:checked"/>

        <!-- Whether the item is shown/visible. -->
        <attr name="android:visible"/>

        <!-- Whether the item is enabled. -->
        <attr name="android:enabled"/>

        <!-- Name of a method on the Context used to inflate the menu that will be
             called when the item is clicked. -->
        <attr name="android:onClick"/>

        <!-- How this item should display in the Action Bar, if present. -->
        <attr name="showAsAction">
            <!-- Never show this item in an action bar, show it in the overflow menu instead.
                 Mutually exclusive with "ifRoom" and "always". -->
            <flag name="never" value="0"/>
            <!-- Show this item in an action bar if there is room for it as determined
                 by the system. Favor this option over "always" where possible.
                 Mutually exclusive with "never" and "always". -->
            <flag name="ifRoom" value="1"/>
            <!-- Always show this item in an actionbar, even if it would override
                 the system's limits of how much stuff to put there. This may make
                 your action bar look bad on some screens. In most cases you should
                 use "ifRoom" instead. Mutually exclusive with "ifRoom" and "never". -->
            <flag name="always" value="2"/>
            <!-- When this item is shown as an action in the action bar, show a text
                 label with it even if it has an icon representation. -->
            <flag name="withText" value="4"/>
            <!-- This item's action view collapses to a normal menu
                 item. When expanded, the action view takes over a
                 larger segment of its container. -->
            <flag name="collapseActionView" value="8"/>
        </attr>

        <!-- An optional layout to be used as an action view.
             See {@link android.view.MenuItem#setActionView(android.view.View)}
             for more info. -->
        <attr format="reference" name="actionLayout"/>

        <!-- The name of an optional View class to instantiate and use as an
             action view. See {@link android.view.MenuItem#setActionView(android.view.View)}
             for more info. -->
        <attr format="string" name="actionViewClass"/>

        <!-- The name of an optional ActionProvider class to instantiate an action view
             and perform operations such as default action for that menu item.
             See {@link android.view.MenuItem#setActionProvider(android.view.ActionProvider)}
             for more info. -->
        <attr format="string" name="actionProviderClass"/>

        <!-- The content description associated with the item. -->
        <attr format="string" name="contentDescription"/>

        <!-- The tooltip text associated with the item. -->
        <attr format="string" name="tooltipText"/>

        <!-- Tint to apply to the icon. -->
        <attr format="color" name="iconTint"/>

        <!-- Blending mode used to apply the icon tint. -->
        <attr name="iconTintMode">
            <!-- The tint is drawn on top of the icon.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the icon. The icon’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the icon, but with the icon’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the icon with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>

    </declare-styleable>
    <declare-styleable name="MenuView">
        <!-- Default appearance of menu item text. -->
        <attr name="android:itemTextAppearance"/>
        <!-- Default horizontal divider between rows of menu items. -->
        <attr name="android:horizontalDivider"/>
        <!-- Default vertical divider between menu items. -->
        <attr name="android:verticalDivider"/>
        <!-- Default background for the menu header. -->
        <attr name="android:headerBackground"/>
        <!-- Default background for each menu item. -->
        <attr name="android:itemBackground"/>
        <!-- Default animations for the menu. -->
        <attr name="android:windowAnimationStyle"/>
        <!-- Default disabled icon alpha for each menu item that shows an icon. -->
        <attr name="android:itemIconDisabledAlpha"/>
        <!-- Whether space should be reserved in layout when an icon is missing. -->
        <attr format="boolean" name="preserveIconSpacing"/>
        <!-- Drawable for the arrow icon indicating a particular item is a submenu. -->
        <attr format="reference" name="subMenuArrow"/>
    </declare-styleable>
    <declare-styleable name="PlayingIndicator">
        <attr format="integer" name="bar_num"/>
        <attr format="integer" name="duration"/>
        <attr format="color|reference" name="bar_color"/>
        <attr format="integer" name="step_num"/>
        <attr format="boolean" name="is_two_way"/>
        <attr format="dimension|reference" name="min_height"/>
    </declare-styleable>
    <declare-styleable name="PopupWindow">
        <!-- Whether the popup window should overlap its anchor view. -->
        <attr format="boolean" name="overlapAnchor"/>
        <attr name="android:popupBackground"/>
        <attr name="android:popupAnimationStyle"/>
    </declare-styleable>
    <declare-styleable name="PopupWindowBackgroundState">
        <!-- State identifier indicating the popup will be above the anchor. -->
        <attr format="boolean" name="state_above_anchor"/>
    </declare-styleable>
    <declare-styleable name="RecycleListView">
        <!-- Bottom padding to use when no buttons are present. -->
        <attr format="dimension" name="paddingBottomNoButtons"/>
        <!-- Top padding to use when no title is present. -->
        <attr format="dimension" name="paddingTopNoTitle"/>
    </declare-styleable>
    <declare-styleable name="RecyclerView">
        <!-- Class name of the Layout Manager to be used.
        <p/>
        The class must extandroidx.recyclerview.widget.RecyclerViewView$LayoutManager
        and have either a default constructor or constructor with the signature
        (android.content.Context, android.util.AttributeSet, int, int).
         <p/>
         If the name starts with a '.', application package is prefixed.
         Else, if the name contains a '.', the classname is assumed to be a full class name.
         Else, the recycler view package naandroidx.appcompat.widgetdget) is prefixed. -->
        <attr format="string" name="layoutManager"/>

        <!-- ============================= -->
        <!-- Attributes for Layout Manager -->
        <!-- ============================= -->
        <eat-comment/>

        <attr name="android:orientation"/>
        <attr name="android:descendantFocusability"/>
        <attr format="integer" name="spanCount"/>
        <attr format="boolean" name="reverseLayout"/>
        <attr format="boolean" name="stackFromEnd"/>
        <attr format="boolean" name="fastScrollEnabled"/>
        <attr format="reference" name="fastScrollVerticalThumbDrawable"/>
        <attr format="reference" name="fastScrollVerticalTrackDrawable"/>
        <attr format="reference" name="fastScrollHorizontalThumbDrawable"/>
        <attr format="reference" name="fastScrollHorizontalTrackDrawable"/>
    </declare-styleable>
    <declare-styleable name="SearchView">
        <!-- The layout to use for the search view. -->
        <attr format="reference" name="layout"/>
        <!-- The default state of the SearchView. If true, it will be iconified when not in
             use and expanded when clicked. -->
        <attr format="boolean" name="iconifiedByDefault"/>
        <!-- An optional maximum width of the SearchView. -->
        <attr name="android:maxWidth"/>
        <!-- An optional user-defined query hint string to be displayed in the empty query field. -->
        <attr format="string" name="queryHint"/>
        <!-- Default query hint used when {@code queryHint} is undefined and
             the search view's {@code SearchableInfo} does not provide a hint. -->
        <attr format="string" name="defaultQueryHint"/>
        <!-- The IME options to set on the query text field. -->
        <attr name="android:imeOptions"/>
        <!-- The input type to set on the query text field. -->
        <attr name="android:inputType"/>
        <!-- Close button icon -->
        <attr format="reference" name="closeIcon"/>
        <!-- Go button icon -->
        <attr format="reference" name="goIcon"/>
        <!-- Search icon -->
        <attr format="reference" name="searchIcon"/>
        <!-- Search icon displayed as a text field hint -->
        <attr format="reference" name="searchHintIcon"/>
        <!-- Voice button icon -->
        <attr format="reference" name="voiceIcon"/>
        <!-- Commit icon shown in the query suggestion row -->
        <attr format="reference" name="commitIcon"/>
        <!-- Layout for query suggestion rows -->
        <attr format="reference" name="suggestionRowLayout"/>
        <!-- Background for the section containing the search query -->
        <attr format="reference" name="queryBackground"/>
        <!-- Background for the section containing the action (e.g. voice search) -->
        <attr format="reference" name="submitBackground"/>
        <attr name="android:focusable"/>
    </declare-styleable>
    <declare-styleable name="Spinner">
        <!-- The prompt to display when the spinner's dialog is shown. -->
        <attr name="android:prompt"/>
        <!-- Theme to use for the drop-down or dialog popup window. -->
        <attr name="popupTheme"/>
        <!-- Background drawable to use for the dropdown in spinnerMode="dropdown". -->
        <attr name="android:popupBackground"/>
        <!-- Width of the dropdown in spinnerMode="dropdown". -->
        <attr name="android:dropDownWidth"/>
        <!-- Reference to an array resource that will populate the Spinner. -->
        <attr name="android:entries"/>
    </declare-styleable>
    <declare-styleable name="StateListDrawable">
        <!-- Indicates whether the drawable should be initially visible. -->
        <attr name="android:visible"/>
        <!-- If true, allows the drawable's padding to change based on the
             current state that is selected.  If false, the padding will
             stay the same (based on the maximum padding of all the states).
             Enabling this feature requires that the owner of the drawable
             deal with performing layout when the state changes, which is
             often not supported. -->
        <attr name="android:variablePadding"/>
        <!-- If true, the drawable's reported internal size will remain
             constant as the state changes; the size is the maximum of all
             of the states.  If false, the size will vary based on the
             current state. -->
        <attr name="android:constantSize"/>
        <!-- Enables or disables dithering of the bitmap if the bitmap does not have the
             same pixel configuration as the screen (for instance: a ARGB 8888 bitmap with
             an RGB 565 screen). -->
        <attr name="android:dither"/>
        <!-- Amount of time (in milliseconds) to fade in a new state drawable. -->
        <attr name="android:enterFadeDuration"/>
        <!-- Amount of time (in milliseconds) to fade out an old state drawable. -->
        <attr name="android:exitFadeDuration"/>
        <!-- Indicates if the drawable needs to be mirrored when its layout direction is
             RTL (right-to-left). -->
        <!--<attr name="autoMirrored"/>-->
    </declare-styleable>
    <declare-styleable name="StateListDrawableItem">
        <!-- Reference to a drawable resource to use for the state. If not
             given, the drawable must be defined by the first child tag. -->
        <attr name="android:drawable"/>
    </declare-styleable>
    <declare-styleable name="SwitchCompat">
        <!-- Drawable to use as the "thumb" that switches back and forth. -->
        <attr name="android:thumb"/>
        <!-- Tint to apply to the thumb drawable. -->
        <attr format="color" name="thumbTint"/>
        <!-- Blending mode used to apply the thumb tint. -->
        <attr name="thumbTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and drawable color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
        <!-- Drawable to use as the "track" that the switch thumb slides within. -->
        <attr format="reference" name="track"/>
        <!-- Tint to apply to the track. -->
        <attr format="color" name="trackTint"/>
        <!-- Blending mode used to apply the track tint. -->
        <attr name="trackTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and drawable color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
        <!-- Text to use when the switch is in the checked/"on" state. -->
        <attr name="android:textOn"/>
        <!-- Text to use when the switch is in the unchecked/"off" state. -->
        <attr name="android:textOff"/>
        <!-- Amount of padding on either side of text within the switch thumb. -->
        <attr format="dimension" name="thumbTextPadding"/>
        <!-- TextAppearance style for text displayed on the switch thumb. -->
        <attr format="reference" name="switchTextAppearance"/>
        <!-- Minimum width for the switch component -->
        <attr format="dimension" name="switchMinWidth"/>
        <!-- Minimum space between the switch and caption text -->
        <attr format="dimension" name="switchPadding"/>
        <!-- Whether to split the track and leave a gap for the thumb drawable. -->
        <attr format="boolean" name="splitTrack"/>
        <!-- Whether to draw on/off text. -->
        <attr format="boolean" name="showText"/>
    </declare-styleable>
    <declare-styleable name="TextAppearance">
        <attr name="android:textSize"/>
        <attr name="android:textColor"/>
        <attr name="android:textColorHint"/>
        <attr name="android:textColorLink"/>
        <attr name="android:textStyle"/>
        <attr name="android:textFontWeight"/>
        <attr name="android:typeface"/>
        <attr name="android:fontFamily"/>
        <attr name="fontFamily"/>
        <attr name="textAllCaps"/>
        <!-- Set the textLocale by a comma-separated language tag string,
             for example "ja-JP,zh-CN". This attribute only takes effect on API 21 and above.
             Before API 24, only the first language tag is used. Starting from API 24,
             the string will be converted into a {@link android.os.LocaleList} and then used by
             {@link android.widget.TextView} -->
        <attr name="textLocale"/>
        <attr name="android:shadowColor"/>
        <attr name="android:shadowDy"/>
        <attr name="android:shadowDx"/>
        <attr name="android:shadowRadius"/>
        <!-- OpenType font variation settings, available aftear api 26. -->
        <attr name="fontVariationSettings"/>
    </declare-styleable>
    <declare-styleable name="Toolbar">
        <attr format="reference" name="titleTextAppearance"/>
        <attr format="reference" name="subtitleTextAppearance"/>
        <attr name="title"/>
        <attr name="subtitle"/>
        <attr name="android:gravity"/>
        <!--  Specifies extra space on the left, start, right and end sides
              of the toolbar's title. Margin values should be positive. -->
        <attr format="dimension" name="titleMargin"/>
        <!--  Specifies extra space on the start side of the toolbar's title.
              If both this attribute and titleMargin are specified, then this
              attribute takes precedence. Margin values should be positive. -->
        <attr format="dimension" name="titleMarginStart"/>
        <!--  Specifies extra space on the end side of the toolbar's title.
              If both this attribute and titleMargin are specified, then this
              attribute takes precedence. Margin values should be positive. -->
        <attr format="dimension" name="titleMarginEnd"/>
        <!--  Specifies extra space on the top side of the toolbar's title.
              If both this attribute and titleMargin are specified, then this
              attribute takes precedence. Margin values should be positive. -->
        <attr format="dimension" name="titleMarginTop"/>
        <!--  Specifies extra space on the bottom side of the toolbar's title.
              If both this attribute and titleMargin are specified, then this
              attribute takes precedence. Margin values should be positive. -->
        <attr format="dimension" name="titleMarginBottom"/>
        <!-- {@deprecated Use titleMargin} -->
        <attr format="dimension" name="titleMargins"/>
        <attr name="contentInsetStart"/>
        <attr name="contentInsetEnd"/>
        <attr name="contentInsetLeft"/>
        <attr name="contentInsetRight"/>
        <attr name="contentInsetStartWithNavigation"/>
        <attr name="contentInsetEndWithActions"/>
        <attr format="dimension" name="maxButtonHeight"/>
        <attr name="buttonGravity">
            <!-- Place object in the vertical center of its container, not changing its size. -->
            <flag name="center_vertical" value="0x10"/>
            <!-- Push object to the top of its container, not changing its size. -->
            <flag name="top" value="0x30"/>
            <!-- Push object to the bottom of its container, not changing its size. -->
            <flag name="bottom" value="0x50"/>
        </attr>
        <!-- Icon drawable to use for the collapse button. -->
        <attr format="reference" name="collapseIcon"/>
        <!-- Text to set as the content description for the collapse button. -->
        <attr format="string" name="collapseContentDescription"/>
        <!-- Reference to a theme that should be used to inflate popups
             shown by widgets in the toolbar. -->
        <attr name="popupTheme"/>
        <!-- Icon drawable to use for the navigation button located at
             the start of the toolbar. -->
        <attr format="reference" name="navigationIcon"/>
        <!-- Text to set as the content description for the navigation button
             located at the start of the toolbar. -->
        <attr format="string" name="navigationContentDescription"/>
        <!-- Drawable to set as the logo that appears at the starting side of
             the Toolbar, just after the navigation button. -->
        <attr name="logo"/>
        <!-- A content description string to describe the appearance of the
             associated logo image. -->
        <attr format="string" name="logoDescription"/>
        <!-- A color to apply to the title string. -->
        <attr format="color" name="titleTextColor"/>
        <!-- A color to apply to the subtitle string. -->
        <attr format="color" name="subtitleTextColor"/>
        <attr name="android:minHeight"/>
        <!-- Menu resource to inflate to be shown in the toolbar -->
        <attr format="reference" name="menu"/>
    </declare-styleable>
    <declare-styleable name="TwinklingRefreshLayout"><attr format="dimension" name="tr_max_head_height"/><attr format="dimension" name="tr_head_height"/><attr format="dimension" name="tr_max_bottom_height"/><attr format="dimension" name="tr_bottom_height"/><attr format="dimension" name="tr_overscroll_height"/><attr format="boolean" name="tr_enable_refresh"/><attr format="boolean" name="tr_enable_loadmore"/><attr format="boolean" name="tr_pureScrollMode_on"/><attr format="boolean" name="tr_overscroll_top_show"/><attr format="boolean" name="tr_overscroll_bottom_show"/><attr format="boolean" name="tr_enable_overscroll"/><attr format="dimension" name="tr_headerView"/><attr format="dimension" name="tr_bottomView"/><attr format="boolean" name="tr_floatRefresh"/><attr format="boolean" name="tr_autoLoadMore"/><attr format="boolean" name="tr_enable_keepIView"/><attr format="boolean" name="tr_showRefreshingWhenOverScroll"/><attr format="boolean" name="tr_showLoadingWhenOverScroll"/></declare-styleable>
    <declare-styleable name="View">
        <!-- Sets the padding, in pixels, of the start edge; see {@link android.R.attr#padding}. -->
        <attr format="dimension" name="paddingStart"/>
        <!-- Sets the padding, in pixels, of the end edge; see {@link android.R.attr#padding}. -->
        <attr format="dimension" name="paddingEnd"/>
        <!-- Boolean that controls whether a view can take focus.  By default the user can not
             move focus to a view; by setting this attribute to true the view is
             allowed to take focus.  This value does not impact the behavior of
             directly calling {@link android.view.View#requestFocus}, which will
             always request focus regardless of this view.  It only impacts where
             focus navigation will try to move focus. -->
        <attr name="android:focusable"/>
        <!-- Deprecated. -->
        <attr format="reference" name="theme"/>
        <!-- Specifies a theme override for a view. When a theme override is set, the
             view will be inflated using a {@link android.content.Context} themed with
             the specified resource. -->
        <attr name="android:theme"/>
    </declare-styleable>
    <declare-styleable name="ViewBackgroundHelper">
        <attr name="android:background"/>
        <!-- Tint to apply to the background. -->
        <attr format="color" name="backgroundTint"/>

        <!-- Blending mode used to apply the background tint. -->
        <attr name="backgroundTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="ViewStubCompat">
        <!-- Supply an identifier for the layout resource to inflate when the ViewStub
             becomes visible or when forced to do so. The layout resource must be a
             valid reference to a layout. -->
        <attr name="android:layout"/>
        <!-- Overrides the id of the inflated View with this value. -->
        <attr name="android:inflatedId"/>
        <attr name="android:id"/>
    </declare-styleable>
</resources>