<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":report" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\report\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":adreport" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\adreport\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":utils" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\utils\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\core\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":ad" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\ad\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":api" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\api\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":player" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\player\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":socket" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\socket\build\intermediates\library_assets\release\out"/></dataSet><dataSet config=":sdk" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\sdk\build\intermediates\library_assets\release\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\main\assets"/><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\build\intermediates\shader_assets\ceshizhuangyongRelease\out"/></dataSet><dataSet config="ceshizhuangyong" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\ceshizhuangyong\assets"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\release\assets"/></dataSet><dataSet config="ceshizhuangyongRelease" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sdk\devlop\kaolaopensdk\demo\src\ceshizhuangyongRelease\assets"/></dataSet></merger>